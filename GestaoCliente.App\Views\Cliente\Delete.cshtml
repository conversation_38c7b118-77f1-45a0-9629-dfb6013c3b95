@model GestaoCliente.App.Models.Cliente

@{
    ViewData["Title"] = "Excluir Cliente";
}

<div class="row">
    <div class="col-12">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h4 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle"></i> @ViewData["Title"]
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning" role="alert">
                    <h5 class="alert-heading">Atenção!</h5>
                    <p>Você está prestes a excluir o cliente <strong>@Html.DisplayFor(model => model.Nome)</strong>.</p>
                    <hr>
                    <p class="mb-0">
                        <strong>Esta ação não pode ser desfeita!</strong> 
                        Todas as cobranças e pagamentos relacionados a este cliente também serão excluídos.
                    </p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h5>Informações do Cliente</h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Nome:</strong></td>
                                <td>@Html.DisplayFor(model => model.Nome)</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>
                                    @if (!string.IsNullOrEmpty(Model.Email))
                                    {
                                        @Model.Email
                                    }
                                    else
                                    {
                                        <span class="text-muted">Não informado</span>
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Telefone:</strong></td>
                                <td>@Html.DisplayFor(model => model.Telefone)</td>
                            </tr>
                            <tr>
                                <td><strong>Empresa:</strong></td>
                                <td>
                                    @if (!string.IsNullOrEmpty(Model.NomeEmpresa))
                                    {
                                        @Html.DisplayFor(model => model.NomeEmpresa)
                                    }
                                    else
                                    {
                                        <span class="text-muted">Não informado</span>
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Data de Cadastro:</strong></td>
                                <td>@Model.DataCriacao.ToString("dd/MM/yyyy HH:mm")</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5>Dados que serão excluídos</h5>
                        <div class="alert alert-info">
                            <ul class="mb-0">
                                <li><strong>@Model.Cobrancas.Count</strong> cobrança(s)</li>
                                <li><strong>@Model.Cobrancas.SelectMany(c => c.Pagamentos).Count()</strong> pagamento(s)</li>
                                @{
                                    var totalValor = Model.Cobrancas.Sum(c => c.ValorTotal);
                                }
                                <li>Valor total: <strong>@totalValor.ToString("C")</strong></li>
                            </ul>
                        </div>

                        @if (Model.Cobrancas.Any())
                        {
                            <div class="alert alert-danger">
                                <strong>Atenção:</strong> Este cliente possui cobranças ativas. 
                                Considere transferir as cobranças para outro cliente antes de excluir.
                            </div>
                        }
                    </div>
                </div>

                <form asp-action="Delete" method="post">
                    <input type="hidden" asp-for="Id" />
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Cancelar
                                </a>
                                <div>
                                    <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info me-2">
                                        <i class="fas fa-eye"></i> Ver Detalhes
                                    </a>
                                    <button type="submit" class="btn btn-danger" 
                                            onclick="return confirm('Tem certeza que deseja excluir este cliente? Esta ação não pode ser desfeita!')">
                                        <i class="fas fa-trash"></i> Confirmar Exclusão
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
