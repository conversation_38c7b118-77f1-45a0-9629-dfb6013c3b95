@model GestaoCliente.App.Models.Pagamento

@{
    ViewData["Title"] = "Novo Pagamento";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">@ViewData["Title"]</h4>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="CobrancaId" class="form-label">Cobrança *</label>
                                <select asp-for="CobrancaId" class="form-select" asp-items="ViewBag.CobrancaId">
                                    <option value="">Selecione uma cobrança</option>
                                </select>
                                <span asp-validation-for="CobrancaId" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Valor" class="form-label">Valor *</label>
                                <div class="input-group">
                                    <span class="input-group-text">R$</span>
                                    <input asp-for="Valor" class="form-control" placeholder="0,00" step="0.01" />
                                </div>
                                <span asp-validation-for="Valor" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="DataVencimento" class="form-label">Data de Vencimento *</label>
                                <input asp-for="DataVencimento" class="form-control" type="date" />
                                <span asp-validation-for="DataVencimento" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input asp-for="Pago" class="form-check-input" type="checkbox" id="pagoSwitch" />
                                    <label class="form-check-label" for="pagoSwitch">
                                        Marcar como pago
                                    </label>
                                </div>
                                <span asp-validation-for="Pago" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="dataPagamentoRow" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="DataPagamento" class="form-label">Data do Pagamento</label>
                                <input asp-for="DataPagamento" class="form-control" type="date" />
                                <span asp-validation-for="DataPagamento" class="text-danger"></span>
                                <small class="form-text text-muted">Se não informada, será definida como hoje</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i data-lucide="info"></i> Informação
                                </h6>
                                <p class="mb-0">
                                    Normalmente os pagamentos são criados automaticamente quando você cria uma cobrança. 
                                    Use este formulário apenas para pagamentos avulsos ou correções.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i data-lucide="arrow-left"></i> Voltar
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i data-lucide="save"></i> Criar Pagamento
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const pagoSwitch = document.getElementById('pagoSwitch');
            const dataPagamentoRow = document.getElementById('dataPagamentoRow');
            const dataPagamentoInput = document.querySelector('input[name="DataPagamento"]');
            
            function toggleDataPagamento() {
                if (pagoSwitch.checked) {
                    dataPagamentoRow.style.display = 'block';
                    // Se não há data definida, definir como hoje
                    if (!dataPagamentoInput.value) {
                        const today = new Date().toISOString().split('T')[0];
                        dataPagamentoInput.value = today;
                    }
                } else {
                    dataPagamentoRow.style.display = 'none';
                    dataPagamentoInput.value = '';
                }
            }
            
            pagoSwitch.addEventListener('change', toggleDataPagamento);
            
            // Verificar estado inicial
            toggleDataPagamento();
        });
    </script>
}
