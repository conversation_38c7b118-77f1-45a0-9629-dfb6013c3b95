//
// _prismjs.scss
//

:not(pre)>code[class*="language-"],
pre[class*="language-"] {
    background: transparent;
    scrollbar-width: thin;
    overflow: hidden !important;

    &:hover {
        overflow: auto !important;
    }
}

code[class*="language-"],
pre[class*="language-"] {
    color: black;
    background: none;
    text-shadow: 0 1px white;
    font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
    font-size: 13px;
    text-align: left;
    white-space: pre;
    word-spacing: normal;
    word-break: normal;
    word-wrap: normal;
    display: block;
    line-height: 1.5;
    tab-size: 4;
    hyphens: none;
}

pre[class*="language-"]::selection,
pre[class*="language-"] ::selection,
code[class*="language-"]::selection,
code[class*="language-"] ::selection {
    text-shadow: none;
    background: #b3d4fc;
}

@media print {

    code[class*="language-"],
    pre[class*="language-"] {
        text-shadow: none;
    }
}

/* Code blocks */
pre[class*="language-"] {
    padding: 1em;
    margin: 0;
    overflow: auto;
}

/* Inline code */
:not(pre)>code[class*="language-"] {
    padding: 0.1em;
    border-radius: 0.3em;
    white-space: normal;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
    color: slategray;
}

.token.punctuation {
    color: var(--#{$prefix}primary);
}

.token.namespace {
    opacity: 0.7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
    color: var(--#{$prefix}primary);
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
    color: var(--#{$prefix}success);
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
    color: #9a6e3a;
    background: transparent;
}

.token.atrule,
.token.attr-value,
.token.keyword {
    color: $danger;
}

.token.function,
.token.class-name {
    color: #dd4a68;
}

.token.regex,
.token.important,
.token.variable {
    color: #e90;
}

.token.important,
.token.bold {
    font-weight: $font-weight-bold;
}

.token.italic {
    font-style: italic;
}

.token.entity {
    cursor: help;
}

code[class*="language-"],
pre[class*="language-"] {
    color: var(--#{$prefix}gray-600);
    text-shadow: none;

    code:not(:last-of-type) {
        margin-bottom: 1rem;
    }
}