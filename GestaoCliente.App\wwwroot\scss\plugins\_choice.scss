//
// _choices.scss
//

.choices {

    &[data-type*="select-one"] {
        cursor: pointer;

        .choices__input {
            padding: $input-padding-y $input-padding-x !important;
            background-color: $input-bg;
            border: 1px solid $input-border-color;
            font-size: $input-font-size;
            border-radius: $input-border-radius;
            color: $input-color;
            margin-bottom: 10px;
        }

        &:after {
            content: "";
            color: var(--#{$prefix}light);
            background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='%239ba6b7'  xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 16a1 1 0 0 1-.64-.23l-6-5a1 1 0 1 1 1.28-1.54L12 13.71l5.36-4.32a1 1 0 0 1 1.41.15a1 1 0 0 1-.14 1.46l-6 4.83A1 1 0 0 1 12 16'/%3E%3C/svg%3E");
            position: absolute;
            border: 0;
            top: 18px;
            right: 12px;
            left: auto;
            font-size: 20px;
            height: 20px;
            width: 20px;
            margin-top: 0;
            transform: translateY(-50%);
            transition: transform 0.2s ease-in-out;
            pointer-events: none;
        }

        &.is-open:after {
            margin-top: 0;
            transform: translateY(-50%) rotate(-180deg);
        }

        .choices__button {
            right: 20px;
        }
    }

    // data-type = select- multiple & text
    &[data-type*="select-multiple"],
    &[data-type*="text"] {
        .choices__inner {
            cursor: text;
            padding: $input-padding-y * 0.5 $input-padding-x * 0.5 !important;
        }

        .choices__button {
            border-left: 1px solid rgba($white, 0.5);
            border-right: 0;
        }

        .choices__list--dropdown {
            padding-bottom: 10px;

            .choices__list {
                margin-bottom: 0;
            }
        }
    }

    &[data-type*="select-multiple"] {
        .choices__input {
            padding-top: 3px;
        }
    }
}

// Select Light
.input-light {
    .choices__inner {
        background-color: var(--#{$prefix}light);
        border: none;
    }
}

.choices {
    transition: box-shadow 0.2s ease;
    box-shadow: $input-box-shadow !important;
    overflow: unset;
}

.choices__inner {
    padding: $input-padding-y $input-padding-x !important;
    vertical-align: middle;
    background-color: $form-select-bg;
    border: $form-select-border-width solid $form-select-border-color;
    border-radius: $form-select-border-radius !important;
    font-size: $input-font-size;
    min-height: auto;

    .choices__list--single {
        padding: 0 !important;
    }
}

.choices__list--multiple {
    .choices__item {
        vertical-align: initial;
        border-radius: 7px;
        padding: 2px 7px;
        font-size: 11px;
        font-weight: $font-weight-normal;
        margin: 2px 5px 2px 0;
        background-color: var(--#{$prefix}primary);
        border: 1px solid var(--#{$prefix}primary);
        color: $white;
        box-shadow: $box-shadow-sm;

        &.is-highlighted {
            background-color: var(--#{$prefix}primary);
            border: 1px solid var(--#{$prefix}primary);
        }
    }
}

.is-disabled {
    .choices__list--multiple {
        .choices__item {
            background-color: var(--#{$prefix}secondary);
            border: 1px solid var(--#{$prefix}secondary);
        }
    }
}

.is-focused .choices__inner, .is-open .choices__inner {
    border-color: $input-focus-border-color;
    box-shadow: $input-focus-box-shadow;
}

.choices__list--dropdown {
    padding: 10px 10px 20px 10px;
    background-color: $card-bg !important;
    border: 1px solid var(--#{$prefix}border-color) !important;
    box-shadow: $dropdown-box-shadow;
    border-radius: $dropdown-border-radius;
    animation-name: DropDownSlideUp;
    animation-duration: 0.3s;
    animation-fill-mode: both;
    will-change: transform;

    &.is-active {
        animation-name: DropDownSlideUp;
    }

    .choices__item--selectable.is-highlighted {
        background-color: $dropdown-link-hover-bg !important;
    }

    .choices__list {
        margin: 0 -10px -16px;

        &::-webkit-scrollbar {
            -webkit-appearance: none;
        }

        &::-webkit-scrollbar:vertical {
            width: 8px;
            margin-right: 15px;
        }

        &::-webkit-scrollbar:horizontal {
            height: 8px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(var(--#{$prefix}dark-rgb), 0.1);
            border-radius: 10px;
            border: 2px solid $card-bg;
        }

        &::-webkit-scrollbar-track {
            border-radius: 10px;
            background-color: $card-bg;
        }
    }

    .has-no-results {
        font-style: italic;
        font-weight: $font-weight-medium;
    }

    .choices__item--selectable:after {
        display: none;
    }
}

.choices__list--dropdown {
    .choices__list {
        max-height: 300px;
    }

    .choices__item {
        font-size: $dropdown-font-size !important;
        padding: $dropdown-item-padding-y $dropdown-item-padding-x $dropdown-item-padding-y 16px !important;
    }
}

.choices__heading {
    font-weight: $font-weight-semibold;
    padding: 10px 16px;
    border-bottom: 1px solid var(--#{$prefix}border-color);
    color: $text-muted;
}

.choices__input {
    background-color: $input-bg;
    color: $input-color;
    font-size: $input-font-size;
    margin-bottom: 0;
    padding: 2px 0 2px 2px;

    &::placeholder {
        color: $input-placeholder-color;
    }
}

.choices__placeholder {
    color: $input-placeholder-color;
    opacity: 1;
}

.choices.is-disabled .choices__inner, .choices.is-disabled .choices__input {
    background-color: var(--#{$prefix}tertiary-bg);
}

[data-bs-theme="dark"] {
    .choices[data-type*="select-one"] {
        .choices__button {
            filter: $btn-close-white-filter;
        }
    }

    .choices {
        &[data-type*="select-one"] {
            &:after {
                background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' fill='%238391a2'  xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 16a1 1 0 0 1-.64-.23l-6-5a1 1 0 1 1 1.28-1.54L12 13.71l5.36-4.32a1 1 0 0 1 1.41.15a1 1 0 0 1-.14 1.46l-6 4.83A1 1 0 0 1 12 16'/%3E%3C/svg%3E");
            }
        }
    }
}