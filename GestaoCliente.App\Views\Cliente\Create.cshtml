@model GestaoCliente.App.Models.Cliente

@{
    ViewData["Title"] = "Novo Cliente";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">@ViewData["Title"]</h4>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Nome" class="form-label">Nome *</label>
                                <input asp-for="Nome" class="form-control" placeholder="Digite o nome completo" />
                                <span asp-validation-for="Nome" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Email" class="form-label">Email</label>
                                <input asp-for="Email" class="form-control" type="email" placeholder="<EMAIL>" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Telefone" class="form-label">Telefone *</label>
                                <input asp-for="Telefone" class="form-control" placeholder="(11) 99999-9999" />
                                <span asp-validation-for="Telefone" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="NomeEmpresa" class="form-label">Nome da Empresa</label>
                                <input asp-for="NomeEmpresa" class="form-control" placeholder="Nome da empresa (opcional)" />
                                <span asp-validation-for="NomeEmpresa" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i data-lucide="arrow-left"></i> Voltar
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i data-lucide="save"></i> Salvar Cliente
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Máscara para telefone
        document.addEventListener('DOMContentLoaded', function() {
            const telefoneInput = document.querySelector('input[name="Telefone"]');
            if (telefoneInput) {
                telefoneInput.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length <= 11) {
                        if (value.length <= 10) {
                            value = value.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
                        } else {
                            value = value.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
                        }
                        e.target.value = value;
                    }
                });
            }
        });
    </script>
}
