using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GestaoCliente.App.Models
{
    public class Pagamento
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "O ID da cobrança é obrigatório")]
        public int CobrancaId { get; set; }

        [Required(ErrorMessage = "O valor é obrigatório")]
        [Column(TypeName = "decimal(18,2)")]
        [Range(0.01, double.MaxValue, ErrorMessage = "O valor deve ser maior que zero")]
        public decimal Valor { get; set; }

        [Required(ErrorMessage = "O status de pagamento é obrigatório")]
        public bool Pago { get; set; } = false;

        public DateTime? DataPagamento { get; set; }

        [Required(ErrorMessage = "A data de vencimento é obrigatória")]
        public DateTime DataVencimento { get; set; }

        public DateTime DataCriacao { get; set; } = DateTime.Now;

        // Relacionamento
        [ForeignKey("CobrancaId")]
        public virtual Cobranca Cobranca { get; set; } = null!;
    }
}
