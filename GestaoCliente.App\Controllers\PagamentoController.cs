using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using GestaoCliente.App.Data;
using GestaoCliente.App.Models;

namespace GestaoCliente.App.Controllers
{
    public class PagamentoController : Controller
    {
        private readonly ApplicationDbContext _context;

        public PagamentoController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Pagamento
        public async Task<IActionResult> Index()
        {
            var pagamentos = await _context.Pagamentos
                .Include(p => p.Cobranc<PERSON>)
                    .ThenInclude(c => c.Cliente)
                .OrderByDescending(p => p.DataVencimento)
                .ToListAsync();
            return View(pagamentos);
        }

        // GET: Pagamento/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var pagamento = await _context.Pagamentos
                .Include(p => p.<PERSON>)
                    .ThenInclude(c => c.Cliente)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (pagamento == null)
            {
                return NotFound();
            }

            return View(pagamento);
        }

        // GET: Pagamento/Create
        public IActionResult Create()
        {
            var cobrancas = _context.Cobrancas
                .Include(c => c.Cliente)
                .Select(c => new { 
                    Id = c.Id, 
                    Display = $"{c.Cliente.Nome} - R$ {c.ValorTotal:F2} ({c.Parcelas}x)" 
                })
                .ToList();

            ViewData["CobrancaId"] = new SelectList(cobrancas, "Id", "Display");
            return View();
        }

        // POST: Pagamento/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("CobrancaId,Valor,Pago,DataPagamento,DataVencimento")] Pagamento pagamento)
        {
            if (ModelState.IsValid)
            {
                pagamento.DataCriacao = DateTime.Now;
                
                // Se está marcado como pago mas não tem data de pagamento, definir como hoje
                if (pagamento.Pago && !pagamento.DataPagamento.HasValue)
                {
                    pagamento.DataPagamento = DateTime.Now;
                }
                
                // Se não está pago, limpar data de pagamento
                if (!pagamento.Pago)
                {
                    pagamento.DataPagamento = null;
                }

                _context.Add(pagamento);
                await _context.SaveChangesAsync();
                TempData["Success"] = "Pagamento criado com sucesso!";
                return RedirectToAction(nameof(Index));
            }

            var cobrancas = _context.Cobrancas
                .Include(c => c.Cliente)
                .Select(c => new { 
                    Id = c.Id, 
                    Display = $"{c.Cliente.Nome} - R$ {c.ValorTotal:F2} ({c.Parcelas}x)" 
                })
                .ToList();

            ViewData["CobrancaId"] = new SelectList(cobrancas, "Id", "Display", pagamento.CobrancaId);
            return View(pagamento);
        }

        // GET: Pagamento/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var pagamento = await _context.Pagamentos.FindAsync(id);
            if (pagamento == null)
            {
                return NotFound();
            }

            var cobrancas = _context.Cobrancas
                .Include(c => c.Cliente)
                .Select(c => new { 
                    Id = c.Id, 
                    Display = $"{c.Cliente.Nome} - R$ {c.ValorTotal:F2} ({c.Parcelas}x)" 
                })
                .ToList();

            ViewData["CobrancaId"] = new SelectList(cobrancas, "Id", "Display", pagamento.CobrancaId);
            return View(pagamento);
        }

        // POST: Pagamento/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,CobrancaId,Valor,Pago,DataPagamento,DataVencimento,DataCriacao")] Pagamento pagamento)
        {
            if (id != pagamento.Id)
            {
                return NotFound();
            }
            ModelState.Remove("Cobranca");
            if (ModelState.IsValid)
            {
                try
                {
                    // Se está marcado como pago mas não tem data de pagamento, definir como hoje
                    if (pagamento.Pago && !pagamento.DataPagamento.HasValue)
                    {
                        pagamento.DataPagamento = DateTime.Now;
                    }
                    
                    // Se não está pago, limpar data de pagamento
                    if (!pagamento.Pago)
                    {
                        pagamento.DataPagamento = null;
                    }

                    _context.Update(pagamento);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = "Pagamento atualizado com sucesso!";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PagamentoExists(pagamento.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            var cobrancas = _context.Cobrancas
                .Include(c => c.Cliente)
                .Select(c => new { 
                    Id = c.Id, 
                    Display = $"{c.Cliente.Nome} - R$ {c.ValorTotal:F2} ({c.Parcelas}x)" 
                })
                .ToList();

            ViewData["CobrancaId"] = new SelectList(cobrancas, "Id", "Display", pagamento.CobrancaId);
            return View(pagamento);
        }

        // GET: Pagamento/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var pagamento = await _context.Pagamentos
                .Include(p => p.Cobranca)
                    .ThenInclude(c => c.Cliente)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (pagamento == null)
            {
                return NotFound();
            }

            return View(pagamento);
        }

        // POST: Pagamento/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var pagamento = await _context.Pagamentos.FindAsync(id);
            if (pagamento != null)
            {
                _context.Pagamentos.Remove(pagamento);
                await _context.SaveChangesAsync();
                TempData["Success"] = "Pagamento excluído com sucesso!";
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: Pagamento/MarcarComoPago/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> MarcarComoPago(int id)
        {
            var pagamento = await _context.Pagamentos.FindAsync(id);
            if (pagamento != null)
            {
                pagamento.Pago = true;
                pagamento.DataPagamento = DateTime.Now;
                await _context.SaveChangesAsync();
                TempData["Success"] = "Pagamento marcado como pago!";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool PagamentoExists(int id)
        {
            return _context.Pagamentos.Any(e => e.Id == id);
        }
    }
}
