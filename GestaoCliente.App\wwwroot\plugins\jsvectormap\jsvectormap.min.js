!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).jsVectorMap=e()}(this,(function(){"use strict";var t=function(t){return function(t){return!!t&&"object"==typeof t}(t)&&!function(t){var i=Object.prototype.toString.call(t);return"[object RegExp]"===i||"[object Date]"===i||function(t){return t instanceof Node}(t)||function(t){return t.$$typeof===e}(t)}(t)};var e="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function i(t,e){return!1!==e.clone&&e.isMergeableObject(t)?o((i=t,Array.isArray(i)?[]:{}),t,e):t;var i}function s(t,e,s){return t.concat(e).map((function(t){return i(t,s)}))}function n(t){return Object.keys(t).concat(function(t){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter((function(e){return t.propertyIsEnumerable(e)})):[]}(t))}function r(t,e){try{return e in t}catch(t){return!1}}function a(t,e,s){var a={};return s.isMergeableObject(t)&&n(t).forEach((function(e){a[e]=i(t[e],s)})),n(e).forEach((function(n){(function(t,e){return r(t,e)&&!(Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))})(t,n)||(r(t,n)&&s.isMergeableObject(e[n])?a[n]=function(t,e){if(!e.customMerge)return o;var i=e.customMerge(t);return"function"==typeof i?i:o}(n,s)(t[n],e[n],s):a[n]=i(e[n],s))})),a}var o=function(e,n,r){(r=r||{}).arrayMerge=r.arrayMerge||s,r.isMergeableObject=r.isMergeableObject||t,r.cloneUnlessOtherwiseSpecified=i;var o=Array.isArray(n);return o===Array.isArray(e)?o?r.arrayMerge(e,n,r):a(e,n,r):i(n,r)},h=function(t){return"object"==typeof t&&void 0!==t.nodeType?t:"string"==typeof t?document.querySelector(t):null},l=function(t,e,i,s){void 0===s&&(s=!1);var n=document.createElement(t);return i&&(n[s?"innerHTML":"textContent"]=i),e&&(n.className=e),n},c=function(t){t.parentNode.removeChild(t)},u=function(t,e,i){return void 0===i&&(i=!1),i?o(t,e):Object.assign(t,e)},p=function(t,e){return t.toLowerCase()+":to:"+e.toLowerCase()},d=function(t,e){Object.assign(t.prototype,e)},f={},m=1,g={on:function(t,e,i,s){void 0===s&&(s={});var n="jvm:"+e+"::"+m++;f[n]={selector:t,handler:i},i._uid=n,t.addEventListener(e,i,s)},delegate:function(t,e,i,s){(e=e.split(" ")).forEach((function(e){g.on(t,e,(function(t){var e=t.target;e.matches(i)&&s.call(e,t)}))}))},off:function(t,e,i){var s=e.split(":")[1];t.removeEventListener(s,i),delete f[i._uid]},flush:function(){Object.keys(f).forEach((function(t){g.off(f[t].selector,t,f[t].handler)}))},getEventRegistry:function(){return f}};var v={onLoaded:"map:loaded",onViewportChange:"viewport:changed",onRegionClick:"region:clicked",onMarkerClick:"marker:clicked",onRegionSelected:"region:selected",onMarkerSelected:"marker:selected",onRegionTooltipShow:"region.tooltip:show",onMarkerTooltipShow:"marker.tooltip:show",onDestroyed:"map:destroyed"},_=function(t,e,i){var s=h(e),n=-1===s.getAttribute("class").indexOf("jvm-region")?"marker":"region",r="region"===n,a=r?s.getAttribute("data-code"):s.getAttribute("data-index"),o=r?v.onRegionSelected:v.onMarkerSelected;return i&&(o=r?v.onRegionTooltipShow:v.onMarkerTooltipShow),{type:n,code:a,event:o,element:r?t.regions[a].element:t._markers[a].element,tooltipText:r?t._mapData.paths[a].name||"":t._markers[a].config.name||""}};function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,s=Array(e);i<e;i++)s[i]=t[i];return s}function b(t,e){for(var i=0;i<e.length;i++){var s=e[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,j(s.key),s)}}function S(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(i)return(i=i.call(t)).next.bind(i);if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return y(t,e);var i={}.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?y(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var s=0;return function(){return s>=t.length?{done:!0}:{done:!1,value:t[s++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function w(){return w=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var s in i)({}).hasOwnProperty.call(i,s)&&(t[s]=i[s])}return t},w.apply(null,arguments)}function k(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,x(t,e)}function M(t,e){if(null==t)return{};var i={};for(var s in t)if({}.hasOwnProperty.call(t,s)){if(-1!==e.indexOf(s))continue;i[s]=t[s]}return i}function x(t,e){return x=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},x(t,e)}function j(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var s=i.call(t,e||"default");if("object"!=typeof s)return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}var C=function(){function t(){}return t.prototype.dispose=function(){this._tooltip?c(this._tooltip):this.shape.remove();for(var t,e=S(Object.getOwnPropertyNames(this));!(t=e()).done;){this[t.value]=null}},t}(),L={getLabelText:function(t,e){if(e){if("function"==typeof e.render){var i=[];return"marker"===this.constructor.Name&&i.push(this.getConfig()),i.push(t),e.render.apply(this,i)}return t}},getLabelOffsets:function(t,e){return"function"==typeof e.offsets?e.offsets(t):Array.isArray(e.offsets)?e.offsets[t]:[0,0]},setStyle:function(t,e){this.shape.setStyle(t,e)},remove:function(){this.shape.remove(),this.label&&this.label.remove()},hover:function(t){this._setStatus("isHovered",t)},select:function(t){this._setStatus("isSelected",t)},_setStatus:function(t,e){this.shape[t]=e,this.shape.updateStyle(),this[t]=e,this.label&&(this.label[t]=e,this.label.updateStyle())}},O=function(t){function e(e){var i,s=e.map,n=e.code,r=e.path,a=e.style,o=e.label,h=e.labelStyle,l=e.labelsGroup;(i=t.call(this)||this)._map=s,i.shape=i._createRegion(r,n,a);var c=i.getLabelText(n,o);if(o&&c){var u=i.shape.getBBox(),p=i.getLabelOffsets(n,o);i.labelX=u.x+u.width/2+p[0],i.labelY=u.y+u.height/2+p[1],i.label=i._map.canvas.createText({text:c,textAnchor:"middle",alignmentBaseline:"central",dataCode:n,x:i.labelX,y:i.labelY},h,l),i.label.addClass("jvm-region jvm-element")}return i}k(e,t);var i=e.prototype;return i._createRegion=function(t,e,i){return(t=this._map.canvas.createPath({d:t,dataCode:e},i)).addClass("jvm-region jvm-element"),t},i.updateLabelPosition=function(){this.label&&this.label.set({x:this.labelX*this._map.scale+this._map.transX*this._map.scale,y:this.labelY*this._map.scale+this._map.transY*this._map.scale})},e}(C);d(O,L);var A=function(t){function e(e,i){var s;return(s=t.call(this)||this)._options=e,s._style={initial:i},s._draw(),s}k(e,t);var i=e.prototype;return i.setStyle=function(t,e){this.shape.setStyle(t,e)},i.getConfig=function(){return this._options.config},i._draw=function(){var t=this._options,e=t.index,i=t.group,s=t.map,n={d:this._getDAttribute(),fill:"none",dataIndex:e};this.shape=s.canvas.createPath(n,this._style,i),this.shape.addClass("jvm-line")},i._getDAttribute=function(){var t=this._options,e=t.x1,i=t.y1,s=t.x2,n=t.y2,r=t.curvature;return"M"+e+","+i+this._getQCommand(e,i,s,n,r)+s+","+n},i._getQCommand=function(t,e,i,s,n){return n?" Q"+((t+i)/2+n*(s-e))+","+((e+s)/2-n*(i-t))+" ":" "},e}(C),X=["curvature"],Y=["curvature"];var T="jvm-",E=T+"element "+T+"marker",P=T+"element "+T+"label",z=function(t){function e(e,i){var s;return(s=t.call(this)||this)._options=e,s._style=i,s._labelX=null,s._labelY=null,s._offsets=null,s._isImage=!!i.initial.image,s._draw(),s._options.label&&s._drawLabel(),s._isImage&&s.updateLabelPosition(),s}k(e,t);var i,s,n,r=e.prototype;return r.getConfig=function(){return this._options.config},r.updateLabelPosition=function(){var t=this._options.map;this.label&&this.label.set({x:this._labelX*t.scale+this._offsets[0]+t.transX*t.scale+5+(this._isImage?(this.shape.width||0)/2:this.shape.node.r.baseVal.value),y:this._labelY*t.scale+t.transY*this._options.map.scale+this._offsets[1]})},r._draw=function(){var t=this._options,e=t.index,i=t.map,s=t.group,n=t.cx,r=t.cy,a=this._isImage?"createImage":"createCircle";this.shape=i.canvas[a]({dataIndex:e,cx:n,cy:r},this._style,s),this.shape.addClass(E)},r._drawLabel=function(){var t=this._options,e=t.index,i=t.map,s=t.label,n=t.labelsGroup,r=t.cx,a=t.cy,o=t.config,h=t.isRecentlyCreated,l=this.getLabelText(e,s);this._labelX=r/i.scale-i.transX,this._labelY=a/i.scale-i.transY,this._offsets=h&&o.offsets?o.offsets:this.getLabelOffsets(e,s),this.label=i.canvas.createText({text:l,dataIndex:e,x:this._labelX,y:this._labelY,dy:"0.6ex"},i.params.markerLabelStyle,n),this.label.addClass(P),h&&this.updateLabelPosition()},i=e,n=[{key:"Name",get:function(){return"marker"}}],(s=null)&&b(i.prototype,s),n&&b(i,n),Object.defineProperty(i,"prototype",{writable:!1}),i}(C);d(z,L);var R=function(){function t(t){void 0===t&&(t={}),this._options=t,this._map=this._options.map,this._series=this._options.series,this._body=l("div","jvm-legend"),this._options.cssClass&&this._body.setAttribute("class",this._options.cssClass),t.vertical?this._map.legendVertical.appendChild(this._body):this._map.legendHorizontal.appendChild(this._body),this.render()}return t.prototype.render=function(){var t,e=this._series.scale.getTicks();if(this._body.innderHTML="",this._options.title){var i=l("div","jvm-legend-title",this._options.title);this._body.appendChild(i)}for(var s=0;s<e.length;s++){var n=l("div","jvm-legend-tick"),r=l("div","jvm-legend-tick-sample");switch(this._series.config.attribute){case"fill":t=e[s].value,/\.(jpg|gif|png)$/.test(t)?r.style.background="url("+e[s].value+")":r.style.background=e[s].value;break;case"stroke":r.style.background=e[s].value;break;case"image":r.style.background="url("+("object"==typeof e[s].value?e[s].value.url:e[s].value)+") no-repeat center center",r.style.backgroundSize="cover"}n.appendChild(r);var a=e[s].label;this._options.labelRender&&(a=this._options.labelRender(a));var o=l("div","jvm-legend-tick-text",a);n.appendChild(o),this._body.appendChild(n)}},t}(),D=function(){function t(t){this._scale=t}var e=t.prototype;return e.getValue=function(t){return this._scale[t]},e.getTicks=function(){var t=[];for(var e in this._scale)t.push({label:e,value:this._scale[e]});return t},t}(),I=function(){function t(t,e,i){void 0===t&&(t={}),this._map=i,this._elements=e,this._values=t.values||{},this.config=t,this.config.attribute=t.attribute||"fill",t.attributes&&this.setAttributes(t.attributes),"object"==typeof t.scale&&(this.scale=new D(t.scale)),this.config.legend&&(this.legend=new R(u({map:this._map,series:this},this.config.legend))),this.setValues(this._values)}var e=t.prototype;return e.setValues=function(t){var e={};for(var i in t)t[i]&&(e[i]=this.scale.getValue(t[i]));this.setAttributes(e)},e.setAttributes=function(t){for(var e in t)this._elements[e]&&this._elements[e].element.setStyle(this.config.attribute,t[e])},e.clear=function(){var t,e={};for(t in this._values)this._elements[t]&&(e[t]=this._elements[t].element.shape.style.initial[this.config.attribute]);this.setAttributes(e),this._values={}},t}();var H={mill:function(t,e,i){return{x:this.radius*(e-i)*this.radDeg,y:-this.radius*Math.log(Math.tan((45+.4*t)*this.radDeg))/.8}},merc:function(t,e,i){return{x:this.radius*(e-i)*this.radDeg,y:-this.radius*Math.log(Math.tan(Math.PI/4+t*Math.PI/360))}},aea:function(t,e,i){var s=i*this.radDeg,n=29.5*this.radDeg,r=45.5*this.radDeg,a=t*this.radDeg,o=e*this.radDeg,h=(Math.sin(n)+Math.sin(r))/2,l=Math.cos(n)*Math.cos(n)+2*h*Math.sin(n),c=h*(o-s),u=Math.sqrt(l-2*h*Math.sin(a))/h,p=Math.sqrt(l-2*h*Math.sin(0))/h;return{x:u*Math.sin(c)*this.radius,y:-(p-u*Math.cos(c))*this.radius}},lcc:function(t,e,i){var s=i*this.radDeg,n=e*this.radDeg,r=33*this.radDeg,a=45*this.radDeg,o=t*this.radDeg,h=Math.log(Math.cos(r)*(1/Math.cos(a)))/Math.log(Math.tan(Math.PI/4+a/2)*(1/Math.tan(Math.PI/4+r/2))),l=Math.cos(r)*Math.pow(Math.tan(Math.PI/4+r/2),h)/h,c=l*Math.pow(1/Math.tan(Math.PI/4+o/2),h),u=l*Math.pow(1/Math.tan(Math.PI/4+0),h);return{x:c*Math.sin(h*(n-s))*this.radius,y:-(u-c*Math.cos(h*(n-s)))*this.radius}}};H.degRad=180/Math.PI,H.radDeg=Math.PI/180,H.radius=6381372;var V={_setupContainerEvents:function(){var t,e,i=this,s=this,n=!1;this.params.draggable&&(g.on(this.container,"mousemove",(function(i){if(!n)return!1;s.transX-=(t-i.pageX)/s.scale,s.transY-=(e-i.pageY)/s.scale,s._applyTransform(),t=i.pageX,e=i.pageY})),g.on(this.container,"mousedown",(function(i){return n=!0,t=i.pageX,e=i.pageY,!1})),g.on(document.body,"mouseup",(function(){n=!1}))),this.params.zoomOnScroll&&g.on(this.container,"wheel",(function(t){var e=75*((t.deltaY||-t.wheelDelta||t.detail)>>10||1),n=i.container.getBoundingClientRect(),r=t.pageX-n.left-window.scrollX,a=t.pageY-n.top-window.scrollY,o=Math.pow(1+s.params.zoomOnScrollSpeed/1e3,-1.5*e);s.tooltip&&s._tooltip.hide(),s._setScale(s.scale*o,r,a),t.preventDefault()}))},_setupElementEvents:function(){var t,e,i,s=this,n=this.container;g.on(n,"mousemove",(function(s){Math.abs(t-s.pageX)+Math.abs(e-s.pageY)>2&&(i=!0)})),g.delegate(n,"mousedown",".jvm-element",(function(s){t=s.pageX,e=s.pageY,i=!1})),g.delegate(n,"mouseover mouseout",".jvm-element",(function(t){var e=_(s,this,!0),i=s.params.showTooltip;"mouseover"===t.type?(e.element.hover(!0),i&&(s._tooltip.text(e.tooltipText),s._emit(e.event,[t,s._tooltip,e.code]),t.defaultPrevented||s._tooltip.show())):(e.element.hover(!1),i&&s._tooltip.hide())})),g.delegate(n,"mouseup",".jvm-element",(function(t){var e=_(s,this);if(!i&&("region"===e.type&&s.params.regionsSelectable||"marker"===e.type&&s.params.markersSelectable)){var n=e.element;s.params[e.type+"sSelectableOne"]&&("region"===e.type?s.clearSelectedRegions():s.clearSelectedMarkers()),e.element.isSelected?n.select(!1):n.select(!0),s._emit(e.event,[e.code,n.isSelected,"region"===e.type?s.getSelectedRegions():s.getSelectedMarkers()])}})),g.delegate(n,"click",".jvm-element",(function(t){var e=_(s,this),i=e.type,n=e.code;s._emit("region"===i?v.onRegionClick:v.onMarkerClick,[t,n])}))},_setupZoomButtons:function(){var t=this,e=this.params.zoomInButton,i=this.params.zoomOutButton,s=function(t){return"string"==typeof t?document.querySelector(t):t},n=e?s(e):l("div","jvm-zoom-btn jvm-zoomin","&#43;",!0),r=i?s(i):l("div","jvm-zoom-btn jvm-zoomout","&#x2212",!0);e||this.container.appendChild(n),i||this.container.appendChild(r);var a=function(e){return void 0===e&&(e=!0),function(){return t._setScale(e?t.scale*t.params.zoomStep:t.scale/t.params.zoomStep,t._width/2,t._height/2,!1,t.params.zoomAnimate)}};g.on(n,"click",a()),g.on(r,"click",a(!1))},_setupContainerTouchEvents:function(){var t,e,i,s,n,r,a,o=this,h=function(h){var l,c,u,p,d=h.touches;if("touchstart"==h.type&&(a=0),1==d.length){var f;if(1==a)u=o.transX,p=o.transY,o.transX-=(i-d[0].pageX)/o.scale,o.transY-=(s-d[0].pageY)/o.scale,null==(f=o._tooltip)||f.hide(),o._applyTransform(),u==o.transX&&p==o.transY||h.preventDefault();i=d[0].pageX,s=d[0].pageY}else if(2==d.length)if(2==a){var m;c=Math.sqrt(Math.pow(d[0].pageX-d[1].pageX,2)+Math.pow(d[0].pageY-d[1].pageY,2))/e,o._setScale(t*c,n,r),null==(m=o._tooltip)||m.hide(),h.preventDefault()}else{var g=o.container.getBoundingClientRect();l={top:g.top+window.scrollY,left:g.left+window.scrollX},n=d[0].pageX>d[1].pageX?d[1].pageX+(d[0].pageX-d[1].pageX)/2:d[0].pageX+(d[1].pageX-d[0].pageX)/2,r=d[0].pageY>d[1].pageY?d[1].pageY+(d[0].pageY-d[1].pageY)/2:d[0].pageY+(d[1].pageY-d[0].pageY)/2,n-=l.left,r-=l.top,t=o.scale,e=Math.sqrt(Math.pow(d[0].pageX-d[1].pageX,2)+Math.pow(d[0].pageY-d[1].pageY,2))}a=d.length};g.on(o.container,"touchstart",h),g.on(o.container,"touchmove",h)},_createRegions:function(){for(var t in this._regionLabelsGroup=this._regionLabelsGroup||this.canvas.createGroup("jvm-regions-labels-group"),this._mapData.paths){var e=new O({map:this,code:t,path:this._mapData.paths[t].path,style:u({},this.params.regionStyle),labelStyle:this.params.regionLabelStyle,labelsGroup:this._regionLabelsGroup,label:this.params.labels&&this.params.labels.regions});this.regions[t]={config:this._mapData.paths[t],element:e}}},_createLines:function(t){var e=!1,i=!1,s=this.params.lineStyle,n=s.curvature,r=M(s,X);for(var a in t){for(var o=t[a],h=0,l=Object.values(this._markers);h<l.length;h++){var c=l[h].config;c.name===o.from&&(e=this.getMarkerPosition(c)),c.name===o.to&&(i=this.getMarkerPosition(c))}if(!1!==e&&!1!==i){var d=o.style||{},f=d.curvature,m=M(d,Y);this._lines[p(o.from,o.to)]=new A({index:a,map:this,group:this._linesGroup,config:o,x1:e.x,y1:e.y,x2:i.x,y2:i.y,curvature:0==f?0:f||n},u(r,m,!0))}}},_createMarkers:function(t,e){var i=this;void 0===t&&(t={}),void 0===e&&(e=!1);var s=function(){var s=t[n],r=i.getMarkerPosition(s),a=s.coords.join(":");if(!r)return 0;if(e){if(Object.keys(i._markers).filter((function(t){return i._markers[t]._uid===a})).length)return 0;n=Object.keys(i._markers).length}var o=new z({index:n,map:i,label:i.params.labels&&i.params.labels.markers,labelsGroup:i._markerLabelsGroup,cx:r.x,cy:r.y,group:i._markersGroup,config:s,isRecentlyCreated:e},u(i.params.markerStyle,w({},s.style||{}),!0));i._markers[n]&&i.removeMarkers([n]),i._markers[n]={_uid:a,config:s,element:o}};for(var n in t)s()},_createSeries:function(){for(var t in this.series={markers:[],regions:[]},this.params.series)for(var e=0;e<this.params.series[t].length;e++)this.series[t][e]=new I(this.params.series[t][e],"markers"===t?this._markers:this.regions,this)},_applyTransform:function(){var t,e,i,s;this._defaultWidth*this.scale<=this._width?(t=(this._width-this._defaultWidth*this.scale)/(2*this.scale),i=(this._width-this._defaultWidth*this.scale)/(2*this.scale)):(t=0,i=(this._width-this._defaultWidth*this.scale)/this.scale),this._defaultHeight*this.scale<=this._height?(e=(this._height-this._defaultHeight*this.scale)/(2*this.scale),s=(this._height-this._defaultHeight*this.scale)/(2*this.scale)):(e=0,s=(this._height-this._defaultHeight*this.scale)/this.scale),this.transY>e?this.transY=e:this.transY<s&&(this.transY=s),this.transX>t?this.transX=t:this.transX<i&&(this.transX=i),this.canvas.applyTransformParams(this.scale,this.transX,this.transY),this._markers&&this._repositionMarkers(),this._lines&&this._repositionLines(),this._repositionLabels()},_resize:function(){var t=this._baseScale;this._width/this._height>this._defaultWidth/this._defaultHeight?(this._baseScale=this._height/this._defaultHeight,this._baseTransX=Math.abs(this._width-this._defaultWidth*this._baseScale)/(2*this._baseScale)):(this._baseScale=this._width/this._defaultWidth,this._baseTransY=Math.abs(this._height-this._defaultHeight*this._baseScale)/(2*this._baseScale)),this.scale*=this._baseScale/t,this.transX*=this._baseScale/t,this.transY*=this._baseScale/t},_setScale:function(t,e,i,s,n){var r,a,o,h,l,c,u,p,d,f,m=this,g=0,_=Math.abs(Math.round(60*(t-this.scale)/Math.max(t,this.scale)));t>this.params.zoomMax*this._baseScale?t=this.params.zoomMax*this._baseScale:t<this.params.zoomMin*this._baseScale&&(t=this.params.zoomMin*this._baseScale),void 0!==e&&void 0!==i&&(r=t/this.scale,s?(d=e+this._defaultWidth*(this._width/(this._defaultWidth*t))/2,f=i+this._defaultHeight*(this._height/(this._defaultHeight*t))/2):(d=this.transX-(r-1)/t*e,f=this.transY-(r-1)/t*i)),n&&_>0?(o=this.scale,h=(t-o)/_,l=this.transX*this.scale,u=this.transY*this.scale,c=(d*t-l)/_,p=(f*t-u)/_,a=setInterval((function(){g+=1,m.scale=o+h*g,m.transX=(l+c*g)/m.scale,m.transY=(u+p*g)/m.scale,m._applyTransform(),g==_&&(clearInterval(a),m._emit(v.onViewportChange,[m.scale,m.transX,m.transY]))}),10)):(this.transX=d,this.transY=f,this.scale=t,this._applyTransform(),this._emit(v.onViewportChange,[this.scale,this.transX,this.transY]))},setFocus:function(t){var e=this;void 0===t&&(t={});var i,s=[];if(t.region?s.push(t.region):t.regions&&(s=t.regions),s.length)return s.forEach((function(t){if(e.regions[t]){var s=e.regions[t].element.shape.getBBox();s&&(i=void 0===i?s:{x:Math.min(i.x,s.x),y:Math.min(i.y,s.y),width:Math.max(i.x+i.width,s.x+s.width)-Math.min(i.x,s.x),height:Math.max(i.y+i.height,s.y+s.height)-Math.min(i.y,s.y)})}})),this._setScale(Math.min(this._width/i.width,this._height/i.height),-(i.x+i.width/2),-(i.y+i.height/2),!0,t.animate);if(t.coords){var n=this.coordsToPoint(t.coords[0],t.coords[1]),r=this.transX-n.x/this.scale,a=this.transY-n.y/this.scale;return this._setScale(t.scale*this._baseScale,r,a,!0,t.animate)}},updateSize:function(){this._width=this.container.offsetWidth,this._height=this.container.offsetHeight,this._resize(),this.canvas.setSize(this._width,this._height),this._applyTransform()},coordsToPoint:function(t,e){var i=st.maps[this.params.map].projection,s=H[i.type](t,e,i.centralMeridian),n=s.x,r=s.y,a=this.getInsetForPoint(n,r);if(!a)return!1;var o=a.bbox;return n=(n-o[0].x)/(o[1].x-o[0].x)*a.width*this.scale,r=(r-o[0].y)/(o[1].y-o[0].y)*a.height*this.scale,{x:n+this.transX*this.scale+a.left*this.scale,y:r+this.transY*this.scale+a.top*this.scale}},getInsetForPoint:function(t,e){for(var i=st.maps[this.params.map].insets,s=0;s<i.length;s++){var n=i[s].bbox,r=n[0],a=n[1];if(t>r.x&&t<a.x&&e>r.y&&e<a.y)return i[s]}},getMarkerPosition:function(t){var e=t.coords;return st.maps[this.params.map].projection?this.coordsToPoint.apply(this,e):{x:e[0]*this.scale+this.transX*this.scale,y:e[1]*this.scale+this.transY*this.scale}},_repositionLines:function(){var t=this,e=this.params.lineStyle.curvature;Object.values(this._lines).forEach((function(i){var s=Object.values(t._markers).find((function(t){return t.config.name===i.getConfig().from})),n=Object.values(t._markers).find((function(t){return t.config.name===i.getConfig().to}));if(s&&n){var r=t.getMarkerPosition(s.config),a=r.x,o=r.y,h=t.getMarkerPosition(n.config),l=h.x,c=h.y,u=0==i._options.curvature?0:i._options.curvature||e,p=(a+l)/2+u*(c-o),d=(o+c)/2-u*(l-a);i.setStyle({d:"M"+a+","+o+" Q"+p+","+d+" "+l+","+c})}}))},_repositionMarkers:function(){for(var t in this._markers){var e=this.getMarkerPosition(this._markers[t].config);!1!==e&&this._markers[t].element.setStyle({cx:e.x,cy:e.y})}},_repositionLabels:function(){var t=this.params.labels;if(t){if(t.regions)for(var e in this.regions)this.regions[e].element.updateLabelPosition();if(t.markers)for(var i in this._markers)this._markers[i].element.updateLabelPosition()}}},B=function(){function t(t,e){this.node=this._createElement(t),e&&this.set(e)}var e=t.prototype;return e._createElement=function(t){return document.createElementNS("http://www.w3.org/2000/svg",t)},e.addClass=function(t){this.node.setAttribute("class",t)},e.getBBox=function(){return this.node.getBBox()},e.set=function(t,e){if("object"==typeof t)for(var i in t)this.applyAttr(i,t[i]);else this.applyAttr(t,e)},e.get=function(t){return this.style.initial[t]},e.applyAttr=function(t,e){this.node.setAttribute(t.replace(/[\w]([A-Z])/g,(function(t){return t[0]+"-"+t[1]})).toLowerCase(),e)},e.remove=function(){c(this.node)},t}(),G=function(t){function e(e,i,s){var n;return void 0===s&&(s={}),(n=t.call(this,e,i)||this).isHovered=!1,n.isSelected=!1,n.style=s,n.style.current={},n.updateStyle(),n}k(e,t);var i=e.prototype;return i.setStyle=function(t,e){var i;"object"==typeof t?u(this.style.current,t):u(this.style.current,((i={})[t]=e,i));this.updateStyle()},i.updateStyle=function(){var t={};u(t,this.style.initial),u(t,this.style.current),this.isHovered&&u(t,this.style.hover),this.isSelected&&(u(t,this.style.selected),this.isHovered&&u(t,this.style.selectedHover)),this.set(t)},e}(B),W=function(t){function e(e,i){return t.call(this,"text",e,i)||this}return k(e,t),e.prototype.applyAttr=function(e,i){"text"===e?this.node.textContent=i:t.prototype.applyAttr.call(this,e,i)},e}(G),N=function(t){function e(e,i){return t.call(this,"image",e,i)||this}return k(e,t),e.prototype.applyAttr=function(e,i){var s;"image"===e?("object"==typeof i?(s=i.url,this.offset=i.offset||[0,0]):(s=i,this.offset=[0,0]),this.node.setAttributeNS("http://www.w3.org/1999/xlink","href",s),this.width=23,this.height=23,this.applyAttr("width",this.width),this.applyAttr("height",this.height),this.applyAttr("x",this.cx-this.width/2+this.offset[0]),this.applyAttr("y",this.cy-this.height/2+this.offset[1])):"cx"==e?(this.cx=i,this.width&&this.applyAttr("x",i-this.width/2+this.offset[0])):"cy"==e?(this.cy=i,this.height&&this.applyAttr("y",i-this.height/2+this.offset[1])):t.prototype.applyAttr.apply(this,arguments)},e}(G),F=function(t){function e(e){var i;return(i=t.call(this,"svg")||this)._container=e,i._defsElement=new B("defs"),i._rootElement=new B("g",{id:"jvm-regions-group"}),i.node.appendChild(i._defsElement.node),i.node.appendChild(i._rootElement.node),i._container.appendChild(i.node),i}k(e,t);var i=e.prototype;return i.setSize=function(t,e){this.node.setAttribute("width",t),this.node.setAttribute("height",e)},i.applyTransformParams=function(t,e,i){this._rootElement.node.setAttribute("transform","scale("+t+") translate("+e+", "+i+")")},i.createPath=function(t,e,i){var s=new G("path",t,e);return s.node.setAttribute("fill-rule","evenodd"),this._add(s,i)},i.createCircle=function(t,e,i){var s=new G("circle",t,e);return this._add(s,i)},i.createLine=function(t,e,i){var s=new G("line",t,e);return this._add(s,i)},i.createText=function(t,e,i){var s=new W(t,e);return this._add(s,i)},i.createImage=function(t,e,i){var s=new N(t,e);return this._add(s,i)},i.createGroup=function(t){var e=new B("g");return this.node.appendChild(e.node),t&&(e.node.id=t),e.canvas=this,e},i._add=function(t,e){return(e=e||this._rootElement).node.appendChild(t.node),t},e}(B),q=function(t){function e(e){var i;i=t.call(this)||this;var s=l("div","jvm-tooltip");return i._map=e,i._tooltip=document.body.appendChild(s),i._bindEventListeners(),i||function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(i)}k(e,t);var i=e.prototype;return i._bindEventListeners=function(){var t=this;g.on(this._map.container,"mousemove",(function(e){if(t._tooltip.classList.contains("active")){var i,s,n=(i=t._map.container,s="#jvm-regions-group",Element.prototype.querySelector.call(i,s)).getBoundingClientRect(),r=t._tooltip.getBoundingClientRect(),a=r.height,o=r.width,h=e.clientY<=n.top+a+5,l=e.pageY-a-5,c=e.pageX-o-5;h&&(l+=a+5,c-=10),e.clientX<n.left+o+5&&(c=e.pageX+5+2,h&&(c+=10)),t.css({top:l+"px",left:c+"px"})}}))},i.getElement=function(){return this._tooltip},i.show=function(){this._tooltip.classList.add("active")},i.hide=function(){this._tooltip.classList.remove("active")},i.text=function(t,e){void 0===e&&(e=!1);var i=e?"innerHTML":"textContent";if(!t)return this._tooltip[i];this._tooltip[i]=t},i.css=function(t){for(var e in t)this._tooltip.style[e]=t[e];return this},e}(C),U=function(){function t(t,e){var i=t.scale,s=t.values;this._scale=i,this._values=s,this._fromColor=this.hexToRgb(i[0]),this._toColor=this.hexToRgb(i[1]),this._map=e,this.setMinMaxValues(s),this.visualize()}var e=t.prototype;return e.setMinMaxValues=function(t){for(var e in this.min=Number.MAX_VALUE,this.max=0,t)(e=parseFloat(t[e]))>this.max&&(this.max=e),e<this.min&&(this.min=e)},e.visualize=function(){var t,e={};for(var i in this._values)t=parseFloat(this._values[i]),isNaN(t)||(e[i]=this.getValue(t));this.setAttributes(e)},e.setAttributes=function(t){for(var e in t)this._map.regions[e]&&this._map.regions[e].element.setStyle("fill",t[e])},e.getValue=function(t){if(this.min===this.max)return"#"+this._toColor.join("");for(var e,i="#",s=0;s<3;s++)i+=(1===(e=Math.round(this._fromColor[s]+(this._toColor[s]-this._fromColor[s])*((t-this.min)/(this.max-this.min))).toString(16)).length?"0":"")+e;return i},e.hexToRgb=function(t){var e=0,i=0,s=0;return 4==t.length?(e="0x"+t[1]+t[1],i="0x"+t[2]+t[2],s="0x"+t[3]+t[3]):7==t.length&&(e="0x"+t[1]+t[2],i="0x"+t[3]+t[4],s="0x"+t[5]+t[6]),[parseInt(e),parseInt(i),parseInt(s)]},t}(),Q="jvm-",$=Q+"container",Z=Q+"markers-group",J=Q+"markers-labels-group",K=Q+"lines-group",tt=Q+"series-container",et=tt+" "+Q+"series-h",it=tt+" "+Q+"series-v",st=function(){function t(e){var i=this;if(void 0===e&&(e={}),this.params=u(t.defaults,e,!0),!t.maps[this.params.map])throw new Error("Attempt to use map which was not loaded: "+e.map);this.regions={},this.scale=1,this.transX=0,this.transY=0,this._mapData=t.maps[this.params.map],this._markers={},this._lines={},this._defaultWidth=this._mapData.width,this._defaultHeight=this._mapData.height,this._height=0,this._width=0,this._baseScale=1,this._baseTransX=0,this._baseTransY=0,"loading"!==document.readyState?this._init():window.addEventListener("DOMContentLoaded",(function(){return i._init()}))}var e=t.prototype;return e._init=function(){var t=this.params;this.container=h(t.selector),this.container.classList.add($),this.canvas=new F(this.container),this.setBackgroundColor(t.backgroundColor),this._createRegions(),this.updateSize(),t.lines&&(this._linesGroup=this.canvas.createGroup(K)),t.markers&&(this._markersGroup=this.canvas.createGroup(Z),this._markerLabelsGroup=this.canvas.createGroup(J)),this._createMarkers(t.markers),this._createLines(t.lines||{}),this._repositionLabels(),this._setupContainerEvents(),this._setupElementEvents(),t.zoomButtons&&this._setupZoomButtons(),t.showTooltip&&(this._tooltip=new q(this)),t.selectedRegions&&this._setSelected("regions",t.selectedRegions),t.selectedMarkers&&this._setSelected("_markers",t.selectedMarkers),t.focusOn&&this.setFocus(t.focusOn),t.visualizeData&&(this.dataVisualization=new U(t.visualizeData,this)),t.bindTouchEvents&&("ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch)&&this._setupContainerTouchEvents(),t.series&&(this.container.appendChild(this.legendHorizontal=l("div",et)),this.container.appendChild(this.legendVertical=l("div",it)),this._createSeries()),this._emit(v.onLoaded,[this])},e.setBackgroundColor=function(t){this.container.style.backgroundColor=t},e.getSelectedRegions=function(){return this._getSelected("regions")},e.clearSelectedRegions=function(t){var e=this;void 0===t&&(t=void 0),(t=this._normalizeRegions(t)||this._getSelected("regions")).forEach((function(t){e.regions[t].element.select(!1)}))},e.setSelectedRegions=function(t){this.clearSelectedRegions(),this._setSelected("regions",this._normalizeRegions(t))},e.getSelectedMarkers=function(){return this._getSelected("_markers")},e.clearSelectedMarkers=function(){this._clearSelected("_markers")},e.setSelectedMarkers=function(t){this._setSelected("_markers",t)},e.addMarkers=function(t){t=Array.isArray(t)?t:[t],this._createMarkers(t,!0)},e.removeMarkers=function(t){var e=this;t||(t=Object.keys(this._markers)),t.forEach((function(t){e._markers[t].element.remove(),delete e._markers[t]}))},e.addLine=function(t,e,i){void 0===i&&(i={}),console.warn("`addLine` method is deprecated, please use `addLines` instead."),this._createLines([{from:t,to:e,style:i}],this._markers,!0)},e.addLines=function(t){var e=this._getLinesAsUids();Array.isArray(t)||(t=[t]),this._createLines(t.filter((function(t){return!(e.indexOf(p(t.from,t.to))>-1)})),!0)},e.removeLines=function(t){var e=this;(t=Array.isArray(t)?t.map((function(t){return p(t.from,t.to)})):this._getLinesAsUids()).forEach((function(t){e._lines[t].dispose(),delete e._lines[t]}))},e.removeLine=function(t,e){console.warn("`removeLine` method is deprecated, please use `removeLines` instead.");var i=p(t,e);this._lines.hasOwnProperty(i)&&(this._lines[i].element.remove(),delete this._lines[i])},e.reset=function(){for(var t in this.series)for(var e=0;e<this.series[t].length;e++)this.series[t][e].clear();this.legendHorizontal&&(c(this.legendHorizontal),this.legendHorizontal=null),this.legendVertical&&(c(this.legendVertical),this.legendVertical=null),this.scale=this._baseScale,this.transX=this._baseTransX,this.transY=this._baseTransY,this._applyTransform(),this.clearSelectedMarkers(),this.clearSelectedRegions(),this.removeMarkers()},e.destroy=function(t){var e=this;void 0===t&&(t=!0),g.flush(),this._tooltip.dispose(),this._emit(v.onDestroyed),t&&Object.keys(this).forEach((function(t){try{delete e[t]}catch(t){}}))},e.extend=function(e,i){if("function"==typeof this[e])throw new Error("The method ["+e+"] does already exist, please use another name.");t.prototype[e]=i},e._emit=function(t,e){for(var i in v)v[i]===t&&"function"==typeof this.params[i]&&this.params[i].apply(this,e)},e._getSelected=function(t){var e=[];for(var i in this[t])this[t][i].element.isSelected&&e.push(i);return e},e._setSelected=function(t,e){var i=this;e.forEach((function(e){i[t][e]&&i[t][e].element.select(!0)}))},e._clearSelected=function(t){var e=this;this._getSelected(t).forEach((function(i){e[t][i].element.select(!1)}))},e._getLinesAsUids=function(){return Object.keys(this._lines)},e._normalizeRegions=function(t){return"string"==typeof t?[t]:t},t}();st.maps={},st.defaults={map:"world",backgroundColor:"transparent",draggable:!0,zoomButtons:!0,zoomOnScroll:!0,zoomOnScrollSpeed:3,zoomMax:12,zoomMin:1,zoomAnimate:!0,showTooltip:!0,zoomStep:1.5,bindTouchEvents:!0,lineStyle:{curvature:0,stroke:"#808080",strokeWidth:1,strokeLinecap:"round"},markersSelectable:!1,markersSelectableOne:!1,markerStyle:{initial:{r:7,fill:"#374151",fillOpacity:1,stroke:"#FFF",strokeWidth:5,strokeOpacity:.5},hover:{fill:"#3cc0ff",cursor:"pointer"},selected:{fill:"blue"},selectedHover:{}},markerLabelStyle:{initial:{fontFamily:"Verdana",fontSize:12,fontWeight:500,cursor:"default",fill:"#374151"},hover:{cursor:"pointer"},selected:{},selectedHover:{}},regionsSelectable:!1,regionsSelectableOne:!1,regionStyle:{initial:{fill:"#dee2e8",fillOpacity:1,stroke:"none",strokeWidth:0},hover:{fillOpacity:.7,cursor:"pointer"},selected:{fill:"#9ca3af"},selectedHover:{}},regionLabelStyle:{initial:{fontFamily:"Verdana",fontSize:"12",fontWeight:"bold",cursor:"default",fill:"#35373e"},hover:{cursor:"pointer"}}},Object.assign(st.prototype,V);var nt=function(){function t(t){if(void 0===t&&(t={}),!t.selector)throw new Error("Selector is not given.");return new st(t)}return t.addMap=function(t,e){st.maps[t]=e},t}();return window.jsVectorMap=nt}));
