﻿﻿
<!DOCTYPE html>
@{
    var html_attributes = ViewBag.HTMLAttributes != null ? ViewBag.HTMLAttributes : "";
    var html_class = ViewBag.HTMLClass != null ? ViewBag.HTMLClass : "";
}
<html lang="en" @html_attributes class="@html_class">

<head>
    @await Html.PartialAsync("~/Views/Shared/Partials/_TitleMeta.cshtml")
    @RenderSection("styles", false)
    @await Html.PartialAsync("~/Views/Shared/Partials/_HeadCSS.cshtml")
</head>

<body>

@RenderBody()

@await Html.PartialAsync("~/Views/Shared/Partials/_FooterScripts.cshtml")
@RenderSection("scripts", required: false)

</body>
</html>