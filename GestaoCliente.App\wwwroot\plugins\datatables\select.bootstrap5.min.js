/*! Bootstrap 5 styling wrapper for Select
 * © SpryMedia Ltd - datatables.net/license
 */
(n=>{var o,d;"function"==typeof define&&define.amd?define(["jquery","datatables.net-bs5","datatables.net-select"],function(e){return n(e,window,document)}):"object"==typeof exports?(o=require("jquery"),d=function(e,t){t.fn.dataTable||require("datatables.net-bs5")(e,t),t.fn.dataTable.select||require("datatables.net-select")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||o(e),d(e,t),n(t,0,e.document)}:(d(window,o),module.exports=n(o,window,window.document))):n(jQuery,window,document)})(function(e,t,n){e=e.fn.dataTable;return e.select.classes.checkbox="form-check-input",e});