// 
// Saas Theme Mode
//

$theme-saas-colors: (
  "primary":   #3f51b5,   // Indigo 500 – professional and balanced
  "secondary": #00bcd4,   // <PERSON>an 500 – modern and energetic
  "success":   #4caf50,   // Green 500 – clean success color
  "info":      #2196f3,   // Blue 500 – informative and calm
  "warning":   #ff9800,   // Orange 500 – friendly warning tone
  "danger":    #f44336,   // Red 500 – clear error alert
  "purple":    #9c27b0,   // Vibrant purple – stylish accent
  "dark":      #1e293b,   // Slate 800 – neutral dark, great for nav
);


@if $theme-saas ==true {

    @import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

    html[data-skin="saas"] {

        --#{$prefix}font-sans-serif:        "Poppins", sans-serif;


        --#{$prefix}body-bg:               #f3f4f7;

        --#{$prefix}border-radius:               .3rem;
        --#{$prefix}border-radius-sm:            .25rem;
        --#{$prefix}border-radius-lg:            .4rem;
        --#{$prefix}border-radius-xl:            .5rem;

        --#{$prefix}font-weight-medium:      500;
        --#{$prefix}font-weight-semibold:    600;
        --#{$prefix}font-weight-bold:        700;

        --#{$prefix}font-size-xxs:          11px;
        --#{$prefix}font-size-xs:           12px;
        --#{$prefix}font-size-base:         13px;
        --#{$prefix}font-size-md:           14px;
        --#{$prefix}font-size-lg:           16px;
        --#{$prefix}font-size-xl:           18px;
        --#{$prefix}font-size-xxl:          20px;

        --#{$prefix}chart-primary:                           #3f51b5;
        --#{$prefix}chart-primary-rgb:                       #{to-rgb(#3f51b5)};
        --#{$prefix}chart-secondary:                         #ff9800;
        --#{$prefix}chart-secondary-rgb:                     #{to-rgb(#ff9800)};
        --#{$prefix}chart-gray:                              #e9eaeb;
        --#{$prefix}chart-gray-rgb:                          #{to-rgb(#e9eaeb)};
        --#{$prefix}chart-dark:                              #2196f3;
        --#{$prefix}chart-dark-rgb:                          #{to-rgb(#2196f3)};

        //Sidenav
        --#{$prefix}sidenav-item-font-weight:                500;
        --#{$prefix}sidenav-item-font-size:                  0.8125rem;
        --#{$prefix}sidenav-sub-item-font-size:              0.7712rem;

        --#{$prefix}theme-card-border-width: 0;
        --#{$prefix}theme-card-box-shadow: 0 1px 2px rgba(45, 52, 59, 0.15);


        @each $name, $value in $theme-saas-colors {
            --#{$prefix}#{$name}: #{$value};
        }

        @each $name, $value in $theme-saas-colors {
            --#{$prefix}#{$name}-rgb: #{to-rgb($value)};
        }

        @each $name, $value in $theme-saas-colors {
            --#{$prefix}#{$name}-bg-subtle: #{rgba($value, 0.15)};
        }

        @each $name, $value in $theme-saas-colors {
            --#{$prefix}#{$name}-border-subtle: #{rgba($value, 0.3)};
        }

        @each $name, $value in $theme-saas-colors {
            --#{$prefix}#{$name}-text-emphasis: #{shade-color($value, 25%)};
        }

        // Background Light left-sidebar
        &[data-sidenav-color="light"] {
            --#{$prefix}sidenav-bg:                       #{$white};
            --#{$prefix}sidenav-border-color:             #{$gray-200};
            --#{$prefix}sidenav-item-color:               #63666a;
            --#{$prefix}sidenav-item-hover-color:         #23303c;
            --#{$prefix}sidenav-item-hover-bg:          #f3f4f6;
            --#{$prefix}sidenav-item-active-color:        #23303c;
            --#{$prefix}sidenav-item-active-bg:         #f3f4f6;
        }

        // Dark Left Sidebar
        &[data-sidenav-color="dark"] {
            --#{$prefix}sidenav-bg: #1c1d28;
            --#{$prefix}sidenav-border-color: #1c1d28;
            --#{$prefix}sidenav-item-color: #6c7889;
            --#{$prefix}sidenav-item-hover-color: #bccee4;
            --#{$prefix}sidenav-item-hover-bg: #20222e;
            --#{$prefix}sidenav-item-active-color: #ced6df;
            --#{$prefix}sidenav-item-active-bg: #20222e;
        }

        // Dark Mode
        &[data-bs-theme="dark"][data-sidenav-color="dark"],
        &[data-bs-theme="dark"][data-sidenav-color="light"] {
            --#{$prefix}sidenav-bg:                     #1e1f27;
            --#{$prefix}sidenav-border-color:           #2c2d38;
            --#{$prefix}sidenav-item-color:             #6c7889;
            --#{$prefix}sidenav-item-hover-color:       #bccee4;
            --#{$prefix}sidenav-item-hover-bg:          #22232c;
            --#{$prefix}sidenav-item-active-color:      #ced6df;
            --#{$prefix}sidenav-item-active-bg:         #22232c;
        }

        // Light Topbar
        &[data-topbar-color="light"] {
            --#{$prefix}topbar-bg:                      #ffffff;
            --#{$prefix}topbar-item-color:                #63666a;
            --#{$prefix}topbar-item-hover-color:          #{$primary};
            --#{$prefix}topbar-search-bg:                 transparent;
            --#{$prefix}topbar-search-border:           #e7e9eb;
        }

        // Dark Topbar
        &[data-topbar-color="dark"] {
            --#{$prefix}topbar-bg:                      #252630;
            --#{$prefix}topbar-item-color:              #adb5bf;
            --#{$prefix}topbar-item-hover-color:        #e0eeff;
            --#{$prefix}topbar-search-bg:               #2d2e3c;
            --#{$prefix}topbar-search-border:           #2d2e3c;
        }

        // Topbar (Dark Mode)
        &[data-bs-theme="dark"][data-topbar-color="light"],
        &[data-bs-theme="dark"][data-topbar-color="dark"]{
            --#{$prefix}topbar-bg:                      #252630;
            --#{$prefix}topbar-item-color:              #adb5bf;
            --#{$prefix}topbar-item-hover-color:        #e0eeff;
            --#{$prefix}topbar-search-bg:               #2d2e3c;
            --#{$prefix}topbar-search-border:           #2d2e3c;
        }
    }

    @if $enable-dark-mode {
        @include color-mode(dark, true) {
            &[data-skin="saas"] {
                --#{$prefix}body-bg:                     #{#17181e};
                --#{$prefix}body-bg-rgb:                 #{to-rgb(#17181e)};

                --#{$prefix}border-color:                #252630;

                --#{$prefix}secondary-bg:                #{#1b1c22};
                --#{$prefix}secondary-bg-rgb:            #{to-rgb(#1b1c22)};

                --#{$prefix}theme-card-border-width: 1px;
                --#{$prefix}theme-card-box-shadow: none;

                --#{$prefix}box-shadow:                  #{0px 0px 30px rgba(0, 0, 0, 0.3)};

                --#{$prefix}light:                     #252630;
                --#{$prefix}light-rgb:                   #{to-rgb(#252630)};
                --#{$prefix}dark:                      #4b4d5c;
                --#{$prefix}dark-rgb:                    #{to-rgb(#4b4d5c)};
            }
        }
    }
}