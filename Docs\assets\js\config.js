!function(){const e=document.documentElement;var i=sessionStorage.getItem("__D_CONFIG__");const t="shadcn",o="light",a={position:"fixed"},s={color:"light"},r={color:"light"},n={size:"default",user:!0};var u={skin:e.getAttribute("data-skin")||t,theme:e.getAttribute("data-bs-theme")||o,layout:{position:e.getAttribute("data-layout-position")||a.position},topbar:{color:e.getAttribute("data-topbar-color")||s.color},menu:{color:e.getAttribute("data-sidenav-color")||r.color},sidenav:{size:e.getAttribute("data-sidenav-size")||n.size,user:e.hasAttribute("data-sidenav-user")||n.user}},i=(window.defaultConfig=structuredClone(u),i?JSON.parse(i):u);if(window.config=i,e.setAttribute("data-skin",i.skin),e.setAttribute("data-bs-theme",i.theme),e.setAttribute("data-sidenav-color",i.menu.color),e.setAttribute("data-topbar-color",i.topbar.color),e.setAttribute("data-layout-position",i.layout.position),i.sidenav.size){let t=i.sidenav.size;window.innerWidth<=767?t="offcanvas":window.innerWidth<=1140&&!["full","fullscreen"].includes(t)&&(t="condensed"),e.setAttribute("data-sidenav-size",t),!0===i.sidenav.user?e.setAttribute("data-sidenav-user","true"):e.removeAttribute("data-sidenav-user")}}();