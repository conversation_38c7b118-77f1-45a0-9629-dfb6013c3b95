@model IEnumerable<GestaoCliente.App.Models.Cobranca>

@{
    ViewData["Title"] = "Cobranças";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="card-title mb-0">@ViewData["Title"]</h4>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Nova Cobrança
                </a>
            </div>
            <div class="card-body">
                @if (TempData["Success"] != null)
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        @TempData["Success"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }

                @if (Model.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Cliente</th>
                                    <th>Valor Total</th>
                                    <th>Parcelas</th>
                                    <th>Vencimento</th>
                                    <th>Status</th>
                                    <th>Data Criação</th>
                                    <th width="200">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    var pagoCount = item.Pagamentos.Count(p => p.Pago);
                                    var totalParcelas = item.Parcelas;
                                    var statusClass = pagoCount == totalParcelas ? "success" : pagoCount > 0 ? "warning" : "danger";
                                    var statusText = pagoCount == totalParcelas ? "Quitado" : pagoCount > 0 ? "Parcial" : "Pendente";
                                    
                                    <tr>
                                        <td>
                                            <strong>@item.Cliente.Nome</strong>
                                            @if (!string.IsNullOrEmpty(item.Cliente.NomeEmpresa))
                                            {
                                                <br><small class="text-muted">@item.Cliente.NomeEmpresa</small>
                                            }
                                        </td>
                                        <td>
                                            <strong class="text-primary">@item.ValorTotal.ToString("C")</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">@item.Parcelas</span>
                                        </td>
                                        <td>
                                            @item.DataVencimento.ToString("dd/MM/yyyy")
                                        </td>
                                        <td>
                                            <span class="badge bg-@statusClass">
                                                @statusText (@pagoCount/@totalParcelas)
                                            </span>
                                        </td>
                                        <td>
                                            @item.DataCriacao.ToString("dd/MM/yyyy")
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-info" title="Detalhes">
                                                    <i data-lucide="eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-warning" title="Editar">
                                                    <i data-lucide="edit"></i>
                                                </a>
                                                <a asp-action="Delete" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-danger" title="Excluir">
                                                    <i data-lucide="trash-2"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i data-lucide="file-text" class="fs-1 text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhuma cobrança cadastrada</h5>
                        <p class="text-muted">Clique no botão "Nova Cobrança" para começar.</p>
                        <a asp-action="Create" class="btn btn-primary">
                            <i data-lucide="plus"></i> Cadastrar Primeira Cobrança
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
