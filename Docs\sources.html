<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Dashboard | Simple - Responsive Bootstrap 5 Admin Dashboard</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Simple is the #1 best-selling admin dashboard template on WrapBootstrap. Perfect for building CRM, CMS, project management tools, and custom web apps with clean UI, responsive design, and powerful features.">
    <meta name="keywords" content="Simple, admin dashboard, WrapBootstrap, HTML template, Bootstrap admin, CRM template, CMS template, responsive admin, web app UI, admin theme, best admin template">
    <meta name="author" content="Coderthemes">

    <!-- App favicon -->
    <link rel="shortcut icon" href="assets/images/favicon.ico">

    <!-- Theme Config Js -->
    <script src="assets/js/config.js"></script>

    <!-- Vendor css -->
    <link href="assets/css/vendor.min.css" rel="stylesheet" type="text/css">

    <!-- App css -->
    <link href="assets/css/app.min.css" rel="stylesheet" type="text/css" id="app-style">

    <!-- Icons css -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/tabler-icons.min.css" rel="stylesheet" type="text/css">
</head>

<body>
    <!-- Begin page -->
    <div class="wrapper">


        <!-- Sidenav Menu Start -->
        <div class="sidenav-menu">

            

            <div class="scrollbar" data-simplebar>

                <!--- Sidenav Menu -->
                <ul class="side-nav">
                    <li class="side-nav-title">Menu</li>

                    <!-- Introduction -->
                    <li class="side-nav-item">
                        <a href="index.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-shield-check"></i></span>
                            <span class="menu-text">Introduction</span>
                        </a>
                    </li>

                    <!-- Folder Structure -->
                    <li class="side-nav-item">
                        <a href="folder-structure.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-folders"></i></span>
                            <span class="menu-text">Folder Structure</span>
                        </a>
                    </li>

                    <!-- Getting Started -->
                    <li class="side-nav-item">
                        <a href="getting-started.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-rocket"></i></span>
                            <span class="menu-text">Getting Started</span>
                        </a>
                    </li>




                    <!-- Theme Setup -->
                    <li class="side-nav-item">
                        <a href="theme-skin-setup.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-diamond"></i></span>
                            <span class="menu-text">Theme Skin Setup</span>
                        </a>
                    </li>

                    <!-- Dark Mode -->
                    <li class="side-nav-item">
                        <a href="dark-mode.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-moon"></i></span>
                            <span class="menu-text">Dark Mode</span>
                        </a>
                    </li>

                    <!-- RTL Version -->
                    <li class="side-nav-item">
                        <a href="rtl-version.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-layout-sidebar-right"></i></span>
                            <span class="menu-text">RTL Version</span>
                        </a>
                    </li>

                    <!-- Sources & Credits -->
                    <li class="side-nav-item">
                        <a href="sources.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-alert-circle"></i></span>
                            <span class="menu-text">Sources & Credits</span>
                        </a>
                    </li>

                    <!-- Changelog -->
                    <li class="side-nav-item">
                        <a href="changelog.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-book"></i></span>
                            <span class="menu-text">Changelog</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <!-- Sidenav Menu End -->

        <!-- Topbar Start -->
        <header class="app-topbar">
            <div class="container-fluid topbar-menu d-flex align-items-center">
                <div class="d-flex align-items-center gap-2">
                    <!-- Topbar Brand Logo -->
                    <div class="logo-topbar">
                        <!-- Logo light -->
                        <a href="index.html" class="logo-light">
                            <span class="logo-lg">
                                <img src="assets/images/logo.png" alt="logo">
                            </span>
                            <span class="logo-sm">
                                <img src="assets/images/logo-sm.png" alt="small logo">
                            </span>
                        </a>

                        <!-- Logo Dark -->
                        <a href="index.html" class="logo-dark">
                            <span class="logo-lg">
                                <img src="assets/images/logo-black.png" alt="dark logo">
                            </span>
                            <span class="logo-sm">
                                <img src="assets/images/logo-sm.png" alt="small logo">
                            </span>
                        </a>
                    </div>



                    
                </div> <!-- .d-flex-->

            </div>
        </header>
        <!-- Topbar End -->



        <!-- ============================================================== -->
        <!-- Start Page Content here -->
        <!-- ============================================================== -->

        <div class="content-page">

            <div class="container mt-3">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">bootstrap </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>bootstrap</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/bootstrap" target="_blank">
                            bootstrap on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">choices.js </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>choices.js</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/choices.js" target="_blank">
                            choices.js on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">clipboard </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>clipboard</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/clipboard" target="_blank">
                            clipboard on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">datatables.net </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>datatables.net</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/datatables.net" target="_blank">
                            datatables.net on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">datatables.net-bs5 </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>datatables.net-bs5</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/datatables.net-bs5" target="_blank">
                            datatables.net-bs5 on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">datatables.net-buttons </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>datatables.net-buttons</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/datatables.net-buttons" target="_blank">
                            datatables.net-buttons on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">datatables.net-buttons-bs5 </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>datatables.net-buttons-bs5</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/datatables.net-buttons-bs5" target="_blank">
                            datatables.net-buttons-bs5 on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">datatables.net-fixedcolumns </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>datatables.net-fixedcolumns</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/datatables.net-fixedcolumns" target="_blank">
                            datatables.net-fixedcolumns on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">datatables.net-fixedcolumns-bs5 </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>datatables.net-fixedcolumns-bs5</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/datatables.net-fixedcolumns-bs5" target="_blank">
                            datatables.net-fixedcolumns-bs5 on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">datatables.net-fixedheader </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>datatables.net-fixedheader</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/datatables.net-fixedheader" target="_blank">
                            datatables.net-fixedheader on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">datatables.net-fixedheader-bs5 </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>datatables.net-fixedheader-bs5</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/datatables.net-fixedheader-bs5" target="_blank">
                            datatables.net-fixedheader-bs5 on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">datatables.net-keytable </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>datatables.net-keytable</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/datatables.net-keytable" target="_blank">
                            datatables.net-keytable on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">datatables.net-keytable-bs5 </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>datatables.net-keytable-bs5</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/datatables.net-keytable-bs5" target="_blank">
                            datatables.net-keytable-bs5 on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">datatables.net-responsive </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>datatables.net-responsive</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/datatables.net-responsive" target="_blank">
                            datatables.net-responsive on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">datatables.net-responsive-bs5 </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>datatables.net-responsive-bs5</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/datatables.net-responsive-bs5" target="_blank">
                            datatables.net-responsive-bs5 on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">datatables.net-select </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>datatables.net-select</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/datatables.net-select" target="_blank">
                            datatables.net-select on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">datatables.net-select-bs5 </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>datatables.net-select-bs5</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/datatables.net-select-bs5" target="_blank">
                            datatables.net-select-bs5 on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">fullcalendar </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>fullcalendar</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/fullcalendar" target="_blank">
                            fullcalendar on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">glightbox </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>glightbox</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/glightbox" target="_blank">
                            glightbox on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">jquery </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>jquery</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/jquery" target="_blank">
                            jquery on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">jsvectormap </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>jsvectormap</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/jsvectormap" target="_blank">
                            jsvectormap on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">jszip </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>jszip</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/jszip" target="_blank">
                            jszip on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">ladda </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>ladda</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/ladda" target="_blank">
                            ladda on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">leaflet </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>leaflet</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/leaflet" target="_blank">
                            leaflet on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">lucide </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>lucide</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/lucide" target="_blank">
                            lucide on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">masonry-layout </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>masonry-layout</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/masonry-layout" target="_blank">
                            masonry-layout on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">moment </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>moment</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/moment" target="_blank">
                            moment on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">muuri </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>muuri</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/muuri" target="_blank">
                            muuri on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">nouislider </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>nouislider</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/nouislider" target="_blank">
                            nouislider on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">pdfmake </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>pdfmake</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/pdfmake" target="_blank">
                            pdfmake on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">prismjs </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>prismjs</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/prismjs" target="_blank">
                            prismjs on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">quill </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>quill</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/quill" target="_blank">
                            quill on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">simplebar </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>simplebar</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/simplebar" target="_blank">
                            simplebar on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">sortablejs </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>sortablejs</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/sortablejs" target="_blank">
                            sortablejs on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">spinkit </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>spinkit</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/spinkit" target="_blank">
                            spinkit on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">sweetalert2 </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>sweetalert2</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/sweetalert2" target="_blank">
                            sweetalert2 on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">typeahead.js </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>typeahead.js</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/typeahead.js" target="_blank">
                            typeahead.js on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">web-animations-js </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>web-animations-js</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/web-animations-js" target="_blank">
                            web-animations-js on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title text-capitalize">wnumb </h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">
                            Learn more about <strong>wnumb</strong> – a useful JavaScript library for enhancing your project functionality.
                        </p>
                        <a class="btn btn-link p-0 fw-semibold text-capitalize" href="https://www.npmjs.com/package/wnumb" target="_blank">
                            wnumb on View Official Website
                            <i class="ti ti-chevron-right ms-1"></i>
                        </a>
                    </div>
                </div>

            </div>
            <!-- container -->

            <!-- Footer Start -->
            <footer class="footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-6 text-center text-md-start">
                            © 2015 -
                            <script>document.write(new Date().getFullYear())</script> Simple By <span class="fw-semibold">Coderthemes</span>
                        </div>
                        <div class="col-md-6">
                            <div class="text-md-end d-none d-md-block">
                                10GB of <span class="fw-bold">250GB</span> Free.
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <!-- end Footer -->

        </div>

        <!-- ============================================================== -->
        <!-- End Page content -->
        <!-- ============================================================== -->

    </div>
    <!-- END wrapper -->


    <!-- Vendor js -->
    <script src="assets/js/vendor.min.js"></script>

    <!-- App js -->
    <script src="assets/js/app.js"></script>

</body>

</html>