@{
    var title = ViewBag.Title;
    var subtitle = ViewBag.SubTitle;
    var badgeIcon = ViewBag.BadgeIcon;
    var badgeTitle = ViewBag.BadgeTitle;
}

<div class="row justify-content-center py-5">
    <div class="col-xxl-5 col-xl-7 text-center">
        @if (!string.IsNullOrEmpty(badgeIcon) && !string.IsNullOrEmpty(badgeTitle))
        {
            <span class="badge badge-default fw-normal shadow px-2 py-1 mb-2 fst-italic fs-xxs">
            <i data-lucide="@badgeIcon" class="fs-sm me-1"></i> @badgeTitle
        </span>
        }
        <h3 class="fw-bold">
            @title
        </h3>
        @if (!string.IsNullOrEmpty(subtitle))
        {
            <p class="fs-md text-muted mb-0">
                @subtitle
            </p>
        }
    </div>
</div>