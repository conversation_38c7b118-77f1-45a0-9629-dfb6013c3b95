class App{init(){this.initComponents(),this.initPreloader(),this.initPortletCard(),this.initMultiDropdown(),this.initFormValidation(),this.initCounter(),this.initCodePreview(),this.initToggle(),this.initDismissible()}initComponents(){"function"==typeof lucide.createIcons&&lucide.createIcons(),document.querySelectorAll('[data-bs-toggle="popover"]').forEach(e=>{new bootstrap.Popover(e)}),document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(e=>{new bootstrap.Tooltip(e)}),document.querySelectorAll(".offcanvas").forEach(e=>{new bootstrap.Offcanvas(e)}),document.querySelectorAll(".toast").forEach(e=>{new bootstrap.Toast(e)});const t=document.getElementById("toastPlacement"),e=document.getElementById("selectToastPlacement"),i=(t&&e&&e.addEventListener("change",function(){var e=t.dataset.originalClass||t.className;t.dataset.originalClass=e,t.className=e+" "+this.value}),document.getElementById("liveAlertPlaceholder")),a=document.getElementById("liveAlertBtn"),o=(a&&a.addEventListener("click",()=>{{var e="Nice, you triggered this alert message!",t="success";const a=document.createElement("div");a.innerHTML=[`<div class="alert alert-${t} alert-dismissible" role="alert">`,`   <div>${e}</div>`,'   <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>',"</div>"].join(""),i&&i.append(a)}}),document.getElementById("app-style"));o&&o.href.includes("rtl.min.css")&&document.documentElement.setAttribute("dir","rtl")}initPreloader(){window.addEventListener("load",()=>{const e=document.getElementById("status"),t=document.getElementById("preloader");e&&(e.style.display="none"),t&&setTimeout(()=>t.style.display="none",350)})}initPortletCard(){$('[data-action="card-close"]').on("click",function(e){e.preventDefault();const t=$(this).closest(".card");t.fadeOut(300,function(){t.remove()})}),$('[data-action="card-toggle"]').on("click",function(e){e.preventDefault();const t=$(this).closest(".card"),a=$(this).find("i").eq(0),i=t.find(".card-body"),o=t.find(".card-footer");i.slideToggle(300),o.slideToggle(200),a.toggleClass("ti-chevron-up ti-chevron-down"),t.toggleClass("card-collapse")});const e=document.querySelectorAll('[data-action="card-refresh"]');e&&e.forEach(function(e){e.addEventListener("click",function(e){e.preventDefault();const t=e.target.closest(".card");let a=t.querySelector(".card-overlay");if(!a){(a=document.createElement("div")).classList.add("card-overlay");const i=document.createElement("div");i.classList.add("spinner-border","text-primary"),a.appendChild(i),t.appendChild(a)}a.style.display="flex",setTimeout(function(){a.style.display="none"},1500)})}),$('[data-action="code-collapse"]').on("click",function(e){e.preventDefault();const t=$(this).closest(".card"),a=$(this).find("i").eq(0),i=t.find(".code-body");i.slideToggle(300),a.toggleClass("ti-chevron-up ti-chevron-down")})}initMultiDropdown(){$(".dropdown-menu a.dropdown-toggle").on("click",function(){var e=$(this).next(".dropdown-menu");const t=$(this).parent().parent().find(".dropdown-menu").not(e);return t.removeClass("show"),t.parent().find(".dropdown-toggle").removeClass("show"),!1})}initFormValidation(){document.querySelectorAll(".needs-validation").forEach(t=>{t.addEventListener("submit",e=>{t.checkValidity()||(e.preventDefault(),e.stopPropagation()),t.classList.add("was-validated")},!1)})}initCounter(){const e=document.querySelectorAll("[data-target]"),t=new IntersectionObserver((e,r)=>{e.forEach(i=>{if(i.isIntersecting){const s=i.target;let e=s.getAttribute("data-target").replace(/,/g,""),t=(e=parseFloat(e),0),a;a=Number.isInteger(e)?Math.floor(e/25):e/25;const n=()=>{t<e?((t+=a)>e&&(t=e),s.innerText=o(t),requestAnimationFrame(n)):s.innerText=o(e)};function o(e){return e%1==0?e.toLocaleString():e.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})}n(),r.unobserve(s)}})},{threshold:1});e.forEach(e=>t.observe(e))}initCodePreview(){Prism.plugins.NormalizeWhitespace.setDefaults({"remove-trailing":!0,"remove-indent":!0,"left-trim":!0,"right-trim":!0,"tabs-to-spaces":4,"spaces-to-tabs":4})}initToggle(){document.querySelectorAll("[data-toggler]").forEach(e=>{const t=e.querySelector("[data-toggler-on]"),a=e.querySelector("[data-toggler-off]");let i="on"===e.getAttribute("data-toggler");const o=()=>{i?(t?.classList.remove("d-none"),a?.classList.add("d-none")):(t?.classList.add("d-none"),a?.classList.remove("d-none"))};t?.addEventListener("click",()=>{i=!1,o()}),a?.addEventListener("click",()=>{i=!0,o()}),o()})}initDismissible(){document.querySelectorAll("[data-dismissible]").forEach(a=>{a.addEventListener("click",()=>{var e=a.getAttribute("data-dismissible");const t=document.querySelector(e);t&&t.remove()})})}}class Layout{init(){this.initLeftSidebar(),this.initTopbarMenu()}initLeftSidebar(){const o=document.querySelector(".side-nav");if(o){o.querySelectorAll("li [data-bs-toggle='collapse']").forEach(e=>{e.addEventListener("click",e=>e.preventDefault())});const i=o.querySelectorAll("li .collapse"),e=(i.forEach(e=>{e.addEventListener("show.bs.collapse",t=>{const a=t.target.closest(".collapse.show");i.forEach(e=>{e!==t.target&&e!==a&&new bootstrap.Collapse(e,{toggle:!1}).hide()})})}),window.location.href.split(/[?#]/)[0]),t=o.querySelectorAll("a");t.forEach(t=>{if(t.href===e){o.querySelectorAll("a.active, li.active, .collapse.show").forEach(e=>{e.classList.remove("active"),e.classList.remove("show")}),t.classList.add("active");let e=t.closest("li");for(;e&&e!==o;){e.classList.add("active");const a=e.closest(".collapse");if(a){new bootstrap.Collapse(a,{toggle:!1}).show();const i=a.closest("li");i&&i.classList.add("active"),e=i}else e=e.parentElement}}}),setTimeout(()=>{var e=o.querySelector("li.active .active"),t=document.querySelector(".sidenav-menu .simplebar-content-wrapper");if(e&&t){e=e.offsetTop-300;if(100<e){var s=t;t=e;var n=600;const r=s.scrollTop,l=t-r;let o=0;!function e(){var t,a,i;o+=20,s.scrollTop=(t=o,a=r,i=l,(t/=n/2)<1?i/2*t*t+a:-i/2*(--t*(t-2)-1)+a),o<n&&setTimeout(e,20)}()}}},200)}}initTopbarMenu(){const e=document.querySelector(".navbar-nav");if(e){const t=window.location.href.split(/[?#]/)[0],a=e.querySelectorAll("li a"),i=(a.forEach(e=>{if(e.href===t){e.classList.add("active");let t=e.parentElement;for(let e=0;e<6&&t&&t!==document.body;e++)"LI"!==t.tagName&&!t.classList.contains("dropdown")||t.classList.add("active"),t=t.parentElement}}),document.querySelector(".navbar-toggle")),o=document.getElementById("navigation");i&&o&&i.addEventListener("click",()=>{i.classList.toggle("open"),"block"===o.style.display?o.style.display="none":o.style.display="block"})}}}class ThemeCustomizer{constructor(){this.html=document.documentElement,this.config={}}initConfig(){this.defaultConfig=JSON.parse(JSON.stringify(window.defaultConfig)),this.config=JSON.parse(JSON.stringify(window.config)),this.setSwitchFromConfig()}changeSkin(e){this.config.skin=e,this.html.setAttribute("data-skin",e),this.setSwitchFromConfig()}changeMenuColor(e){this.config.menu.color=e,this.html.setAttribute("data-sidenav-color",e),this.setSwitchFromConfig()}changeLeftbarSize(e,t=!0){this.html.setAttribute("data-sidenav-size",e),t&&(this.config.sidenav.size=e,this.setSwitchFromConfig())}changeLayoutPosition(e){this.config.layout.position=e,this.html.setAttribute("data-layout-position",e),this.setSwitchFromConfig()}changeLayoutColor(e){this.config.theme=e,this.html.setAttribute("data-bs-theme",e),this.setSwitchFromConfig()}changeTopbarColor(e){this.config.topbar.color=e,this.html.setAttribute("data-topbar-color",e),this.setSwitchFromConfig()}changeSidebarUser(e){(this.config.sidenav.user=e)?this.html.setAttribute("data-sidenav-user",e):this.html.removeAttribute("data-sidenav-user"),this.setSwitchFromConfig()}resetTheme(){this.config=JSON.parse(JSON.stringify(window.defaultConfig)),this.changeSkin(this.config.skin),this.changeMenuColor(this.config.menu.color),this.changeLeftbarSize(this.config.sidenav.size),this.changeLayoutColor(this.config.theme),this.changeLayoutPosition(this.config.layout.position),this.changeTopbarColor(this.config.topbar.color),this.changeSidebarUser(this.config.sidenav.user),this._adjustLayout()}setSwitchFromConfig(){const e=this.config;sessionStorage.setItem("__D_CONFIG__",JSON.stringify(e)),document.querySelectorAll(".right-bar input[type=checkbox]").forEach(e=>e.checked=!1);{var t='input[name="sidebar-user"]',a=!0===e.sidenav.user;const i=document.querySelector(t);i&&(i.checked=a)}[["data-skin",e.skin],["data-bs-theme",e.theme],["data-layout-position",e.layout.position],["data-topbar-color",e.topbar.color],["data-sidenav-color",e.menu.color],["data-sidenav-size",e.sidenav.size]].forEach(([e,t])=>{const a=document.querySelector(`input[name="${e}"][value="${t}"]`);a&&(a.checked=!0)})}initSwitchListener(){var e=(e,t)=>{document.querySelectorAll(e).forEach(e=>e.addEventListener("change",()=>t(e)))};e('input[name="data-skin"]',e=>this.changeSkin(e.value)),e('input[name="data-sidenav-color"]',e=>this.changeMenuColor(e.value)),e('input[name="data-sidenav-size"]',e=>this.changeLeftbarSize(e.value)),e('input[name="data-bs-theme"]',e=>this.changeLayoutColor(e.value)),e('input[name="data-layout-position"]',e=>this.changeLayoutPosition(e.value)),e('input[name="data-topbar-color"]',e=>this.changeTopbarColor(e.value)),e('input[name="sidebar-user"]',e=>this.changeSidebarUser(e.checked));const t=document.getElementById("light-dark-mode"),a=(t&&t.addEventListener("click",()=>{var e="light"===this.config.theme?"dark":"light";this.changeLayoutColor(e)}),document.querySelector("#reset-layout")),i=(a&&a.addEventListener("click",()=>this.resetTheme()),document.querySelector(".sidenav-toggle-button")),o=(i&&i.addEventListener("click",()=>this._toggleSidebar()),document.querySelector(".button-close-fullsidebar"));o&&o.addEventListener("click",()=>{this.html.classList.remove("sidebar-enable"),this.hideBackdrop()}),document.querySelectorAll(".button-sm-hover").forEach(e=>{e.addEventListener("click",()=>{var e=this.html.getAttribute("data-sidenav-size");this.changeLeftbarSize("sm-hover-active"===e?"sm-hover":"sm-hover-active",!1)})})}_toggleSidebar(){var e=this.html.getAttribute("data-sidenav-size"),t=this.config.sidenav.size;"offcanvas"===e?this.showBackdrop():"fullscreen"===t?this.changeLeftbarSize("fullscreen"===e?"default":"fullscreen",!1):this.changeLeftbarSize("condensed"===e?"default":"condensed",!1),this.html.classList.toggle("sidebar-enable")}showBackdrop(){const e=document.createElement("div");e.id="custom-backdrop",e.className="offcanvas-backdrop fade show",document.body.appendChild(e),document.body.style.overflow="hidden",767<window.innerWidth&&(document.body.style.paddingRight="15px"),e.addEventListener("click",()=>{this.html.classList.remove("sidebar-enable"),this.hideBackdrop()})}hideBackdrop(){var e=document.getElementById("custom-backdrop");e&&(document.body.removeChild(e),document.body.style.overflow="",document.body.style.paddingRight="")}_adjustLayout(){var e=window.innerWidth,t=this.config.sidenav.size;e<=767.98?this.changeLeftbarSize("offcanvas",!1):e<=1140&&!["offcanvas","fullscreen"].includes(t)?this.changeLeftbarSize("condensed",!1):this.changeLeftbarSize(t)}initWindowSize(){window.addEventListener("resize",()=>this._adjustLayout())}init(){this.initConfig(),this.initSwitchListener(),this.initWindowSize(),this._adjustLayout(),this.setSwitchFromConfig()}}class Plugins{init(){this.initFlatPicker(),this.initTouchSpin()}initFlatPicker(){document.querySelectorAll("[data-provider]").forEach(e=>{var t=e.getAttribute("data-provider");const a=e.attributes,i={disableMobile:!0,defaultDate:new Date};if("flatpickr"===t)a["data-date-format"]&&(i.dateFormat=a["data-date-format"].value),a["data-enable-time"]&&(i.enableTime=!0,i.dateFormat+=" H:i"),a["data-altFormat"]&&(i.altInput=!0,i.altFormat=a["data-altFormat"].value),a["data-minDate"]&&(i.minDate=a["data-minDate"].value),a["data-maxDate"]&&(i.maxDate=a["data-maxDate"].value),a["data-default-date"]&&(i.defaultDate=a["data-default-date"].value),a["data-multiple-date"]&&(i.mode="multiple"),a["data-range-date"]&&(i.mode="range"),a["data-inline-date"]&&(i.inline=!0,i.defaultDate=a["data-default-date"].value),a["data-disable-date"]&&(i.disable=a["data-disable-date"].value.split(",")),a["data-week-number"]&&(i.weekNumbers=!0),flatpickr(e,i);else if("timepickr"===t){const o={enableTime:!0,noCalendar:!0,dateFormat:"H:i",defaultDate:new Date};a["data-time-hrs"]&&(o.time_24hr=!0),a["data-min-time"]&&(o.minTime=a["data-min-time"].value),a["data-max-time"]&&(o.maxTime=a["data-max-time"].value),a["data-default-time"]&&(o.defaultDate=a["data-default-time"].value),a["data-time-inline"]&&(o.inline=!0,o.defaultDate=a["data-time-inline"].value),flatpickr(e,o)}})}initTouchSpin(){document.querySelectorAll("[data-touchspin]").forEach(e=>{const t=e.querySelector("[data-minus]"),a=e.querySelector("[data-plus]"),i=e.querySelector("input");if(i){const o=Number(i.min),s=Number(i.max??0),n=Number.isFinite(o),r=Number.isFinite(s),l=()=>Number.parseInt(i.value)||0;i.hasAttribute("readonly")||(t&&t.addEventListener("click",()=>{let e=l()-1;(!n||e>=o)&&(i.value=e.toString())}),a&&a.addEventListener("click",()=>{let e=l()+1;(!r||e<=s)&&(i.value=e.toString())}))}})}}class I18nManager{constructor(e="en",t="/assets/data/translations/",a="#selected-language-image",i="[data-lang]",o="data-lang",s="[data-translator-lang]"){this.selectedLanguage=localStorage.getItem("__T_LANG__")||e,this.langPath=t,this.langImageSelector=a,this.translationKeySelector=i,this.translationKeyAttribute=o,this.languageSelector=s,this.selectedLanguageImage=document.querySelector(this.langImageSelector),this.languageButtons=document.querySelectorAll(this.languageSelector)}async init(){await this.applyTranslations(),this.updateSelectedImage(),this.bindLanguageSwitchers()}async loadTranslations(){try{const e=await fetch(""+this.langPath+this.selectedLanguage+".json");if(e.ok)return await e.json();throw new Error(`Failed to load ${this.selectedLanguage}.json`)}catch(e){return console.error("Translation load error:",e),{}}}async applyTranslations(){const i=await this.loadTranslations();document.querySelectorAll(this.translationKeySelector).forEach(e=>{var t=e.getAttribute(this.translationKeyAttribute),a=(a=i,t.split(".").reduce((e,t)=>e&&void 0!==e[t]?e[t]:null,a));a?e.innerHTML=a:console.warn("Missing translation for key: "+t)})}setLanguage(e){this.selectedLanguage=e,localStorage.setItem("__T_LANG__",e),this.applyTranslations(),this.updateSelectedImage()}updateSelectedImage(){var e=document.querySelector(`[data-translator-lang="${this.selectedLanguage}"] [data-translator-image]`);e&&this.selectedLanguageImage&&(this.selectedLanguageImage.src=e.src)}bindLanguageSwitchers(){this.languageButtons.forEach(t=>{t.addEventListener("click",()=>{var e=t.dataset.translatorLang;e&&e!==this.selectedLanguage&&this.setLanguage(e)})})}}document.addEventListener("DOMContentLoaded",function(e){(new App).init(),(new Layout).init(),(new ThemeCustomizer).init(),(new Plugins).init()});const ins=(e,t=1)=>{var a=getComputedStyle(document.documentElement).getPropertyValue("--bs-"+e).trim();return e.includes("-rgb")?`rgba(${a}, ${t})`:a};function debounce(e,t){let a;return function(){clearTimeout(a),a=setTimeout(e,t)}}class CustomApexChart{constructor({selector:e,series:t=[],options:a={},colors:i=["#727cf5","#0acf97","#fa5c7c"]}){if(e){this.selector=e,this.series=t,this.options=a,this.colors=i,this.selector instanceof HTMLElement?this.element=this.selector:this.element=document.querySelector(this.selector),this.chart=null;try{this.render(),CustomApexChart.instances.push(this)}catch(e){console.error("CustomApexChart: Error during chart initialization:",e)}}else console.warn("CustomApexChart: 'selector' is required.")}getColors(){if(this.element){const t=this.element.getAttribute("data-colors");if(!t)return this.colors;var e=t.split(",").map(e=>e.trim());return e.length?e:this.colors}}render(){if(this.chart&&this.chart.destroy(),this.element){var t=this.getColors();let e=JSON.parse(JSON.stringify(this.options));(e=this.injectDynamicColors(e,t)).colors||(e.colors=t),e.series||(e.series=this.series),this.chart=new ApexCharts(this.element,e),this.chart.render()}else console.warn(`CustomApexChart: No element found for selector '${this.selector}'.`)}injectDynamicColors(e,a){if("boxplot"===e.chart?.type?.toLowerCase()&&(e.plotOptions=e.plotOptions||{},e.plotOptions.boxPlot=e.plotOptions.boxPlot||{},e.plotOptions.boxPlot.colors=e.plotOptions.boxPlot.colors||{},e.plotOptions.boxPlot.colors.upper=e.plotOptions.boxPlot.colors.upper||a[0],e.plotOptions.boxPlot.colors.lower=e.plotOptions.boxPlot.colors.lower||a[1]),Array.isArray(e.yaxis)&&e.yaxis.forEach((e,t)=>{t=a[t]||this.colors[t]||"#999";e.axisBorder=e.axisBorder||{},e.axisBorder.color=e.axisBorder.color||t,e.labels=e.labels||{},e.labels.style=e.labels.style||{},e.labels.style.color=e.labels.style.color||t}),e.markers&&!e.markers.strokeColor&&(e.markers.strokeColor=a),"gradient"===e.fill?.type&&e.fill.gradient&&(e.fill.gradient.gradientToColors=e.fill.gradient.gradientToColors||a),e.plotOptions?.treemap?.colorScale?.ranges){const t=e.plotOptions.treemap.colorScale.ranges;0<t.length&&!t[0].color&&(t[0].color=a[0]),1<t.length&&!t[1].color&&(t[1].color=a[1])}return e}static rerenderAll(){if(0<CustomApexChart.instances.length)for(const e of CustomApexChart.instances)e.render()}}class CustomEChart{constructor({selector:e,options:t={},theme:a=null,initOptions:i={}}){if(e){this.selector=e,this.element=null,this.getOptions=t,this.theme=a,this.initOptions=i,this.chart=null;try{this.render(),CustomEChart.instances.push(this)}catch(e){console.error("CustomEChart: Initialization error",e)}}else console.warn("CustomEChart: 'selector' is required.")}render(){try{var e;this.selector instanceof HTMLElement?this.element=this.selector:this.element=document.querySelector(this.selector),this.chart&&this.chart.dispose(),this.element?(e=this.getOptions(),this.chart=echarts.init(this.element,this.theme,this.initOptions),this.chart.setOption(e),window.addEventListener("resize",debounce(()=>{this.chart.resize()},200))):console.warn(`CustomEChart: No element found for selector '${this.selector}'.`)}catch(e){console.error(`CustomEChart: Render error for '${this.selector}'`,e)}}static reSizeAll(){if(0<CustomEChart.instances.length)for(const e of CustomEChart.instances)e.element&&null!==e.element.offsetParent&&requestAnimationFrame(()=>{e.chart.on("finished",()=>{requestAnimationFrame(()=>{e.chart.resize()})})})}static rerenderAll(){if(0<CustomEChart.instances.length)for(const e of CustomEChart.instances)e.render()}}CustomApexChart.instances=[],CustomEChart.instances=[];const themeObserver=new MutationObserver(()=>{CustomApexChart.rerenderAll(),CustomEChart.rerenderAll()}),menuObserver=(themeObserver.observe(document.documentElement,{attributes:!0,attributeFilter:["data-bs-theme","data-skin"]}),new MutationObserver(()=>{requestAnimationFrame(()=>{CustomEChart.reSizeAll()})}));menuObserver.observe(document.documentElement,{attributes:!0,attributeFilter:["data-sidenav-size"]});