
@{
    ViewBag.Subtitle = "Charts";
    ViewBag.Title = "Bubble Apexchart";
}

@section styles
{
    <link href="/plugins/jsvectormap/jsvectormap.min.css" rel="stylesheet" type="text/css"/>
}

<div class="container-fluid">

    @await Html.PartialAsync("~/Views/Shared/Partials/_PageTitle.cshtml")


    <div class="row">
        <!-- Repeat for each map -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header d-block">
                    <h5 class="mb-1 card-title">World Vector Map</h5>
                    <p class="text-muted mb-0">A global map showing countries with interactive markers.</p>
                </div>
                <div class="card-body">
                    <div id="world-map-markers" style="height: 360px"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header d-block">
                    <h5 class="mb-1 card-title">World Vector Live Map</h5>
                    <p class="text-muted mb-0">Live dynamic vector representation of the world with real-time
                        features.</p>
                </div>
                <div class="card-body">
                    <div id="world-map-markers-line" style="height: 360px"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header d-block">
                    <h5 class="mb-1 card-title">US Vector Map</h5>
                    <p class="text-muted mb-0">Interactive vector map of the United States with state-level details.</p>
                </div>
                <div class="card-body">
                    <div id="usa-vector-map" style="height: 360px"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header d-block">
                    <h5 class="mb-1 card-title">India Vector Map</h5>
                    <p class="text-muted mb-0">Detailed vector map of India with region highlights.</p>
                </div>
                <div class="card-body">
                    <div id="india-vector-map" style="height: 360px"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header d-block">
                    <h5 class="mb-1 card-title">Canada Vector Map</h5>
                    <p class="text-muted mb-0">Displays Canadian provinces and territories with interactive regions.</p>
                </div>
                <div class="card-body">
                    <div id="canada-vector-map" style="height: 360px"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header d-block">
                    <h5 class="mb-1 card-title">Russia Vector Map</h5>
                    <p class="text-muted mb-0">Interactive map highlighting major regions across Russia.</p>
                </div>
                <div class="card-body">
                    <div id="russia-vector-map" style="height: 360px"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header d-block">
                    <h5 class="mb-1 card-title">Iraq Vector Map</h5>
                    <p class="text-muted mb-0">Vector visualization of Iraq highlighting provinces and regions.</p>
                </div>
                <div class="card-body">
                    <div id="iraq-vector-map" style="height: 360px"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header d-block">
                    <h5 class="mb-1 card-title">Spain Vector Map</h5>
                    <p class="text-muted mb-0">Geographical map of Spain with region-based interaction.</p>
                </div>
                <div class="card-body">
                    <div id="spain-vector-map" style="height: 360px"></div>
                </div>
            </div>
        </div>
    </div>
</div>

@section scripts
{
    <script src="/plugins/jsvectormap/jsvectormap.min.js"></script>
    <script src="/js/maps/world-merc.js"></script>
    <script src="/js/maps/world.js"></script>
    <script src="/js/maps/in-mill-en.js"></script>
    <script src="/js/maps/canada.js"></script>
    <script src="/js/maps/iraq.js"></script>
    <script src="/js/maps/russia.js"></script>
    <script src="/js/maps/spain.js"></script>
    <script src="/js/maps/us-aea-en.js"></script>
    <script src="/js/maps/us-lcc-en.js"></script>
    <script src="/js/maps/us-mill-en.js"></script>
    <script src="/js/pages/maps-vector.js"></script>
}
