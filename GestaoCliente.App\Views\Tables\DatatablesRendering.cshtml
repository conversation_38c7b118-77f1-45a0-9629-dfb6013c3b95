
@{
    ViewBag.Title = "Data Rendering";
    ViewBag.SubTitle = "Customize how data is displayed in tables using render functions, templates, and conditional formatting with DataTables.";
    ViewBag.BadgeIcon = "layout-template";
    ViewBag.BadgeTitle = "Custom Output";
}

@section styles
{
    <link href="/plugins/datatables/responsive.bootstrap5.min.css" rel="stylesheet" type="text/css"/>
}

<div class="container-fluid">

    @await Html.PartialAsync("~/Views/Shared/Partials/_PageTitle.cshtml")


    <div class="row justify-content-center">
        <div class="col-xxl-12">
            <div class="card">
                <div class="card-header justify-content-between">
                    <h5 class="card-title"> Example </h5>
                    <a class="icon-link icon-link-hover link-primary fw-semibold"
                       href="https://datatables.net/examples/basic_init/data_rendering" target="_blank">View Docs <i
                            class="ti ti-arrow-right bi align-middle fs-lg"></i></a>
                </div>
                <div class="card-body">
                    <table class="table table-striped dt-responsive align-middle mb-0" id="datatable-rendering">
                        <thead class="thead-sm text-uppercase fs-xxs">
                        <tr>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Office</th>
                            <th>Progress</th>
                            <th>Start date</th>
                            <th>Salary</th>
                        </tr>
                        </thead>
                    </table>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div>
    </div>
</div>
<!-- container -->

@section scripts
{
    <script src="/plugins/datatables/dataTables.min.js"></script>
    <script src="/plugins/datatables/dataTables.bootstrap5.min.js"></script>
    <script src="/plugins/datatables/dataTables.responsive.min.js"></script>
    <script src="/plugins/datatables/responsive.bootstrap5.min.js"></script>
    <script src="/js/pages/datatables-rendering.js"></script>
}
