<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Dashboard | Simple - Responsive Bootstrap 5 Admin Dashboard</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Simple is the #1 best-selling admin dashboard template on WrapBootstrap. Perfect for building CRM, CMS, project management tools, and custom web apps with clean UI, responsive design, and powerful features.">
    <meta name="keywords" content="Simple, admin dashboard, WrapBootstrap, HTML template, Bootstrap admin, CRM template, CMS template, responsive admin, web app UI, admin theme, best admin template">
    <meta name="author" content="Coderthemes">

    <!-- App favicon -->
    <link rel="shortcut icon" href="assets/images/favicon.ico">

    <!-- Theme Config Js -->
    <script src="assets/js/config.js"></script>

    <!-- Vendor css -->
    <link href="assets/css/vendor.min.css" rel="stylesheet" type="text/css">

    <!-- App css -->
    <link href="assets/css/app.min.css" rel="stylesheet" type="text/css" id="app-style">

    <!-- Icons css -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/tabler-icons.min.css" rel="stylesheet" type="text/css">
</head>

<body>
    <!-- Begin page -->
    <div class="wrapper">


        <!-- Sidenav Menu Start -->
        <div class="sidenav-menu">

            

            <div class="scrollbar" data-simplebar>

                <!--- Sidenav Menu -->
                <ul class="side-nav">
                    <li class="side-nav-title">Menu</li>

                    <!-- Introduction -->
                    <li class="side-nav-item">
                        <a href="index.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-shield-check"></i></span>
                            <span class="menu-text">Introduction</span>
                        </a>
                    </li>

                    <!-- Folder Structure -->
                    <li class="side-nav-item">
                        <a href="folder-structure.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-folders"></i></span>
                            <span class="menu-text">Folder Structure</span>
                        </a>
                    </li>

                    <!-- Getting Started -->
                    <li class="side-nav-item">
                        <a href="getting-started.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-rocket"></i></span>
                            <span class="menu-text">Getting Started</span>
                        </a>
                    </li>




                    <!-- Theme Setup -->
                    <li class="side-nav-item">
                        <a href="theme-skin-setup.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-diamond"></i></span>
                            <span class="menu-text">Theme Skin Setup</span>
                        </a>
                    </li>

                    <!-- Dark Mode -->
                    <li class="side-nav-item">
                        <a href="dark-mode.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-moon"></i></span>
                            <span class="menu-text">Dark Mode</span>
                        </a>
                    </li>

                    <!-- RTL Version -->
                    <li class="side-nav-item">
                        <a href="rtl-version.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-layout-sidebar-right"></i></span>
                            <span class="menu-text">RTL Version</span>
                        </a>
                    </li>

                    <!-- Sources & Credits -->
                    <li class="side-nav-item">
                        <a href="sources.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-alert-circle"></i></span>
                            <span class="menu-text">Sources & Credits</span>
                        </a>
                    </li>

                    <!-- Changelog -->
                    <li class="side-nav-item">
                        <a href="changelog.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-book"></i></span>
                            <span class="menu-text">Changelog</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <!-- Sidenav Menu End -->

        <!-- Topbar Start -->
        <header class="app-topbar">
            <div class="container-fluid topbar-menu d-flex align-items-center">
                <div class="d-flex align-items-center gap-2">
                    <!-- Topbar Brand Logo -->
                    <div class="logo-topbar">
                        <!-- Logo light -->
                        <a href="index.html" class="logo-light">
                            <span class="logo-lg">
                                <img src="assets/images/logo.png" alt="logo">
                            </span>
                            <span class="logo-sm">
                                <img src="assets/images/logo-sm.png" alt="small logo">
                            </span>
                        </a>

                        <!-- Logo Dark -->
                        <a href="index.html" class="logo-dark">
                            <span class="logo-lg">
                                <img src="assets/images/logo-black.png" alt="dark logo">
                            </span>
                            <span class="logo-sm">
                                <img src="assets/images/logo-sm.png" alt="small logo">
                            </span>
                        </a>
                    </div>



                    
                </div> <!-- .d-flex-->

            </div>
        </header>
        <!-- Topbar End -->



        <!-- ============================================================== -->
        <!-- Start Page Content here -->
        <!-- ============================================================== -->

        <div class="content-page">

          <div class="container mt-3">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Setup</h3>
              </div>
              <div class="card-body">
                <h4>Prerequisites</h4>
  
                <p>
                  Please follow below steps to install and setup all
                  prerequisites:
                </p>
  
                <ul>
                  <li>
                    <h5>.NET</h5>
                    <p class="mb-2">
                      Make sure to have the
                      <a
                        href="https://dotnet.microsoft.com/en-us/download"
                        target="_blank"
                        >.NET</a
                      >
                      installed &amp; running in your computer. If you already
                      have installed it then can skip this step.
                    </p>
                  </li>
  
                  <li>
                    <h5>Visual Studio 2022</h5>
                    <p class="mb-2">
                      Make sure to have the
                      <a
                        href="https://visualstudio.microsoft.com/vs/"
                        target="_blank"
                        >Visual Studio 2022</a
                      >
                      installed &amp; running in your computer. If you already
                      have installed it then you can skip this step.
                    </p>
                  </li>
                </ul>
                <h4 class="mt-4">Installation</h4>
  
                <p class="mt-2">
                  If you are using <strong>Visual Studio 2022</strong>:
                </p>
                <ul>
                  <li class="mb-2">
                    Make sure to have the latest version installed.
                  </li>
                  <li>
                    Open Simple.sln file in Visual Studio & Hit run button (On
                    topbar, green play icon).
                  </li>
                </ul>
  
                <strong>OR</strong>
  
                <p class="mt-1">
                  You can run the following commands to run project locally or
                  build for production use:
                </p>
                <table class="table table-bordered m-0">
                  <thead>
                    <tr>
                      <th style="width: 20%"><i class="ti-file"></i> Command</th>
                      <th>Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>dotnet watch</code></td>
                      <td>
                        This command will build and start the app in "watch" mode.
                        The app will automatically rebuild and refresh whenever
                        you make code changes. It’s useful for active development.
                      </td>
                    </tr>
  
                    <tr>
                      <td><code>dotnet run</code></td>
                      <td>
                        This command will build and start the app. It doesn’t
                        track code changes, so you'll need to stop and rerun the
                        command manually after changes.
                      </td>
                    </tr>
  
                    <tr>
                      <td><code>dotnet build</code></td>
                      <td>This command will build the app.</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
  
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Setup For SCSS <span class="text-muted">(Optional)</span></h3>
              </div>
              <div class="card-body">
                <h4>Prerequisites</h4>
  
                <p>
                  Please follow below steps to install and setup all
                  prerequisites:
                </p>
  
                <ul>
                  <li>
                    <h5>Node.js</h5>
                    <p class="mb-2">
                      In order to use build tools you will need to download
                      and install Node.js. If you do not have Node.js installed already,
                      you can get it by downloading the package installer from the
                      official website. Please download the stable version of Node.js
                      (LTS).</p><a href="https://nodejs.org/" target="_blank">Download
                      Node.js</a>
                  </li>
  
                  <li>
                    <h5>Gulp &amp; Gulp CLI</h5>
                    <p>Make sure to have the <a href="https://gulpjs.com/" target="_blank">Gulp</a>
                      installed &amp; running in your
                      computer. If you already have installed gulp on your computer, you
                      can skip this step. In order to install, just run command <code>npm
                          install -g gulp@4.0.1</code> from your terminal.</p>
                  </li>
                </ul>
                <h4 class="mt-4">Installation</h4>
  
                <h5 class="fw-normal">You can run this app using one of these package managers
                  <strong>Yarn</strong>, <strong>NPM</strong>.</h5>
  
              <div class="mt-3">
                  <h5 class="mb-1"><span class="font-semibold text-blue-500">1. Yarn</h5>
                  <p class="mb-2 text-gray-800">If you don't have <strong>yarn</strong> installed, use the
                      next command <code>npm i -g yarn</code> or <code>sudo npm i -g yarn </code></p>
              </div>
  
              <table class="table table-bordered m-0">
                  <thead>
                      <tr>
                          <th style="width:20%"><i class="ti-file"></i> Command</th>
                          <th>Description</th>
                      </tr>
                  </thead>
                  <tbody>
                      <tr>
                          <td><code>yarn</code></td>
                          <td>This would install all required dependencies in <code>node_modules</code>
                              folder.</td>
                      </tr>
                      <tr>
                          <td><code>yarn dev</code> or <code>gulp</code></td>
                          <td>This would compile plugin, scss and generates css in assets folder.
                          </td>
                      </tr>
                      <tr>
                          <td><code>yarn build</code> or <code>gulp build</code></td>
                          <td>It compiles the source assets including scss and bundles into production ready
                              use.</td>
                      </tr>
                      <tr>
                          <td><code>yarn rtl</code> or <code>gulp rtl</code></td>
                          <td>
                              <p class="text-muted">
                                  This would compile plugin, scss and generates rtl css in assets folder.
                              </p>
                          </td>
                      </tr>
                      <tr>
                          <td><code>yarn rtl-build</code> or <code>gulp rtlBuild</code></td>
                          <td>
                              <p class="text-muted">
                                  It compiles the source assets including scss and bundles into production
                                  ready use RTL
                              </p>
                          </td>
                      </tr>
                  </tbody>
              </table>
  
              <div class="mt-3">
                  <h5 class="mb-1"><span class="font-semibold text-blue-500">2. NPM</h5>
                  <p class="mb-2 text-gray-800"><strong>npm</strong> comes preinstalled when you install
                      Nodejs</p>
              </div>
  
              <table class="table table-bordered m-0">
                  <thead>
                      <tr>
                          <th style="width:20%"><i class="ti-file"></i> Command</th>
                          <th>Description</th>
                      </tr>
                  </thead>
                  <tbody>
                      <tr>
                          <td><code>npm i</code> or <code>npm i --force</code></td>
                          <td>This would install all required dependencies in <code>node_modules</code>
                              folder.</td>
                      </tr>
                      <tr>
                          <td><code>npm run dev</code> or <code>gulp</code></td>
                          <td>This would compile all the resources in assets folder.</td>
                      </tr>
                      <tr>
                          <td><code>npm run build</code> or <code>gulp build</code></td>
                          <td>It compiles the source assets including scss and bundles into production ready
                              use.</td>
                      </tr>
                      <tr>
                          <td><code>npm run rtl</code> or <code>gulp rtl</code></td>
                          <td>
                              <p class="text-muted">
                                  This would compile plugin, scss and generates rtl css in assets folder.
                              </p>
                          </td>
                      </tr>
                      <tr>
                          <td><code>npm run rtl-build</code> or <code>gulp rtlBuild</code></td>
                          <td>
                              <p class="text-muted">
                                  It compiles the source assets including scss and bundles into production
                                  ready use RTL
                              </p>
                          </td>
                      </tr>
                  </tbody>
              </table>
  
              </div>
            </div>
          </div>
            <!-- container -->

            <!-- Footer Start -->
            <footer class="footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-6 text-center text-md-start">
                            © 2015 -
                            <script>document.write(new Date().getFullYear())</script> Simple By <span class="fw-semibold">Coderthemes</span>
                        </div>
                        <div class="col-md-6">
                            <div class="text-md-end d-none d-md-block">
                                10GB of <span class="fw-bold">250GB</span> Free.
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <!-- end Footer -->

        </div>

        <!-- ============================================================== -->
        <!-- End Page content -->
        <!-- ============================================================== -->

    </div>
    <!-- END wrapper -->


    <!-- Vendor js -->
    <script src="assets/js/vendor.min.js"></script>

    <!-- App js -->
    <script src="assets/js/app.js"></script>

</body>

</html>