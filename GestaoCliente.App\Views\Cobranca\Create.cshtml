@model GestaoCliente.App.Models.Cobranca

@{
    ViewData["Title"] = "Nova Cobrança";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">@ViewData["Title"]</h4>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="ClienteId" class="form-label">Cliente *</label>
                                <select asp-for="ClienteId" class="form-select" asp-items="ViewBag.ClienteId">
                                    <option value="">Selecione um cliente</option>
                                </select>
                                <span asp-validation-for="ClienteId" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="ValorTotal" class="form-label">Valor Total *</label>
                                <div class="input-group">
                                    <span class="input-group-text">R$</span>
                                    <input asp-for="ValorTotal" class="form-control" placeholder="0,00" step="0.01" />
                                </div>
                                <span asp-validation-for="ValorTotal" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Parcelas" class="form-label">Número de Parcelas *</label>
                                <input asp-for="Parcelas" class="form-control" type="number" min="1" placeholder="1" />
                                <span asp-validation-for="Parcelas" class="text-danger"></span>
                                <small class="form-text text-muted">Os pagamentos serão criados automaticamente</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="DataVencimento" class="form-label">Data de Vencimento da 1ª Parcela *</label>
                                <input asp-for="DataVencimento" class="form-control" type="date" />
                                <span asp-validation-for="DataVencimento" class="text-danger"></span>
                                <small class="form-text text-muted">As demais parcelas vencerão mensalmente</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle"></i> Informação
                                </h6>
                                <p class="mb-0">
                                    Após criar a cobrança, os pagamentos serão gerados automaticamente com base no número de parcelas. 
                                    O valor será dividido igualmente entre as parcelas, e cada uma terá vencimento mensal.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Voltar
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Criar Cobrança
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Formatação de valor monetário
        document.addEventListener('DOMContentLoaded', function() {
            const valorInput = document.querySelector('input[name="ValorTotal"]');
            const parcelasInput = document.querySelector('input[name="Parcelas"]');
            
            if (valorInput && parcelasInput) {
                function updatePreview() {
                    const valor = parseFloat(valorInput.value) || 0;
                    const parcelas = parseInt(parcelasInput.value) || 1;
                    const valorParcela = valor / parcelas;
                    
                    // Você pode adicionar aqui uma prévia do valor por parcela
                    console.log(`Valor por parcela: R$ ${valorParcela.toFixed(2)}`);
                }
                
                valorInput.addEventListener('input', updatePreview);
                parcelasInput.addEventListener('input', updatePreview);
            }
        });
    </script>
}
