//
// _flatpickr.scss
//

.flatpickr-calendar {
    background: $dropdown-bg;
    box-shadow: var(--#{$prefix}box-shadow);
    border: 1px solid $dropdown-border-color;
    font-size: $font-size-base;

    &.arrowTop {
        &:before {
            border-bottom-color: $dropdown-bg;
        }

        &:after {
            border-bottom-color: $dropdown-bg;
        }
    }

    &.arrowBottom {

        &:before,
        &:after {
            border-top-color: $dropdown-bg;
        }
    }

    &.open {
        z-index: 999;
    }
}

.flatpickr-current-month {
    font-size: 100%;
}

.flatpickr-day {

    &.selected,
    &.startRange,
    &.endRange,
    &.selected.inRange,
    &.startRange.inRange,
    &.endRange.inRange,
    &.selected:focus,
    &.startRange:focus,
    &.endRange:focus,
    &.selected:hover,
    &.startRange:hover,
    &.endRange:hover,
    &.selected.prevMonthDay,
    &.startRange.prevMonthDay,
    &.endRange.prevMonthDay,
    &.selected.nextMonthDay,
    &.startRange.nextMonthDay,
    &.endRange.nextMonthDay {
        background: var(--#{$prefix}primary);
        border-color: var(--#{$prefix}primary);
    }

    &.selected.startRange+.endRange:not(:nth-child(7n+1)),
    &.startRange.startRange+.endRange:not(:nth-child(7n+1)),
    &.endRange.startRange+.endRange:not(:nth-child(7n+1)) {
        box-shadow: -10px 0 0 var(--#{$prefix}primary);
    }
}

.flatpickr-time {

    input:hover,
    .flatpickr-am-pm:hover,
    input:focus,
    .flatpickr-am-pm:focus {
        background: $input-bg;
        color: $dropdown-link-active-color;
    }
}

.flatpickr-months {
    .flatpickr-month {
        height: 40px;
    }

    .flatpickr-prev-month,
    .flatpickr-next-month,
    .flatpickr-month {
        color: var(--#{$prefix}body-color);
        fill: var(--#{$prefix}gray-500);
        line-height: 16px;
    }
}

.flatpickr-weekdays {
    background-color: $dropdown-link-active-bg;
}

span.flatpickr-weekday,
.flatpickr-day,
.flatpickr-current-month input.cur-year[disabled],
.flatpickr-current-month input.cur-year[disabled]:hover,
.flatpickr-time input,
.flatpickr-time .flatpickr-time-separator,
.flatpickr-time .flatpickr-am-pm {
    color: $dropdown-link-color;
    fill: $dropdown-link-color;
    font-weight: $font-weight-semibold;
}

.flatpickr-current-month input.cur-year {
    font-weight: $font-weight-semibold;
}

.flatpickr-day {

    &.disabled,
    &.disabled:hover,
    &.prevMonthDay,
    &.nextMonthDay,
    &.notAllowed,
    &.notAllowed.prevMonthDay,
    &.notAllowed.nextMonthDay {
        color: var(--#{$prefix}secondary-color);
    }

    &.inRange,
    &.prevMonthDay.inRange,
    &.nextMonthDay.inRange,
    &.today.inRange,
    &.prevMonthDay.today.inRange,
    &.nextMonthDay.today.inRange,
    &:hover,
    &.prevMonthDay:hover,
    &.nextMonthDay:hover,
    &:focus,
    &.prevMonthDay:focus,
    &.nextMonthDay:focus {
        background: $dropdown-link-hover-bg;
        border-color: $dropdown-link-hover-bg;
    }
}

.flatpickr-calendar.showTimeInput.hasTime .flatpickr-time {
    border-top: 1px solid $dropdown-bg;
}

.numInputWrapper:hover,
.flatpickr-current-month .flatpickr-monthDropdown-months:hover {
    background-color: transparent;
    color: $dropdown-link-active-color;
}

.flatpickr-current-month .numInputWrapper {
    width: 8ch;
    display: inline-block;
    margin-left: 10px;
}

.flatpickr-current-month .flatpickr-monthDropdown-months,
.flatpickr-current-month .numInputWrapper {
    border: 1px solid var(--#{$prefix}border-color);
    border-radius: 4px;
    padding: 2px 8px;
    font-weight: normal;
}

.flatpickr-day.inRange {
    box-shadow: -5px 0 0 $dropdown-link-hover-bg, 5px 0 0 $dropdown-link-hover-bg;
}

.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover {
    color: var(--#{$prefix}secondary-color);
    text-decoration: line-through;
    opacity: 0.5;
}

.flatpickr-calendar.hasTime .flatpickr-time {
    border-top: var(--#{$prefix}border-color);
}

.flatpickr-weekwrapper .flatpickr-weeks {
    box-shadow: none;

}

.flatpickr-weekwrapper span.flatpickr-day,
.flatpickr-weekwrapper span.flatpickr-day:hover {
    color: var(--#{$prefix}secondary-color);
}

[data-inline-date=true],
[data-time-inline] {
    display: none
}