//
// _tour.scss
//

.tg-backdrop {
    z-index: 1001;
}

.tg-dialog {
    background: $card-bg !important;
    color: var(--#{$prefix}body-color);

    .tg-arrow {
        background: $card-bg !important;
    }
}

.tg-dialog-btn {
    background-color: var(--#{$prefix}primary) !important;
    color: $white !important;
    border-color: var(--#{$prefix}primary) !important;
}

.tg-dialog-close-btn {
    height: 20px !important;
    width: 20px !important;
    background-color: rgba(var(--#{$prefix}warning-rgb), 0.75);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}