/*! Select for DataTables 3.0.1
 * © SpryMedia Ltd - datatables.net/license/mit
 */
(s=>{var l,c;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return s(e,window,document)}):"object"==typeof exports?(l=require("jquery"),c=function(e,t){t.fn.dataTable||require("datatables.net")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||l(e),c(e,t),s(t,e,e.document)}:(c(window,l),module.exports=s(l,window,window.document))):s(jQuery,window,document)})(function(m,i,a){var v=m.fn.dataTable;function r(n,e,t){function s(t,s){s<t&&(e=s,s=t,t=e);var e,l=!1;return n.columns(":visible").indexes().filter(function(e){return e===t&&(l=!0),e===s?!(l=!1):l})}function l(t,s){var e,l=n.rows({search:"applied"}).indexes(),c=(l.indexOf(t)>l.indexOf(s)&&(e=s,s=t,t=e),!1);return l.filter(function(e){return e===t&&(c=!0),e===s?!(c=!1):c})}var c,t=n.cells({selected:!0}).any()||t?(c=s(t.column,e.column),l(t.row,e.row)):(c=s(0,e.column),l(0,e.row)),t=n.cells(t,c).flatten();n.cells(e,{selected:!0}).any()?n.cells(t).deselect():n.cells(t).select()}function w(e){var t=v.select.classes.checkbox;return e?t.replace(/ /g,"."):t}function n(e){var t=e.settings()[0]._select.selector;m(e.table().container()).off("mousedown.dtSelect",t).off("mouseup.dtSelect",t).off("click.dtSelect",t),m("body").off("click.dtSelect"+b(e.table().node()))}function c(o){var a,t=m(o.table().container()),s=o.settings()[0],l=s._select.selector;t.on("mousedown.dtSelect",l,function(e){(e.shiftKey||e.metaKey||e.ctrlKey)&&t.css("-moz-user-select","none").one("selectstart.dtSelect",l,function(){return!1}),i.getSelection&&(a=i.getSelection())}).on("mouseup.dtSelect",l,function(){t.css("-moz-user-select","")}).on("click.dtSelect",l,function(e){var t,s=o.select.items();if(a){var l=i.getSelection();if((!l.anchorNode||m(l.anchorNode).closest("table")[0]===o.table().node())&&l!==a)return}var c,l=o.settings()[0],n=o.table().container();m(e.target).closest("div.dt-container")[0]==n&&(n=o.cell(m(e.target).closest("td, th"))).any()&&(c=m.Event("user-select.dt"),d(o,c,[s,n,e]),c.isDefaultPrevented()||(c=n.index(),"row"===s?(t=c.row,p(e,o,l,"row",t)):"column"===s?(t=n.index().column,p(e,o,l,"column",t)):"cell"===s&&(t=n.index(),p(e,o,l,"cell",t)),l._select_lastCell=c))}),m("body").on("click.dtSelect"+b(o.table().node()),function(e){var t;!s._select.blurable||m(e.target).parents().filter(o.table().container()).length||0===m(e.target).parents("html").length||m(e.target).parents("div.DTE").length||(t=m.Event("select-blur.dt"),d(o,t,[e.target,e]),t.isDefaultPrevented())||_(s,!0)})}function d(e,t,s,l){l&&!e.flatten().length||("string"==typeof t&&(t+=".dt"),s.unshift(e),m(e.table().node()).trigger(t,s))}function g(e){return e.mRender&&"selectCheckbox"===e.mRender._name}function l(l,e){var t,s,c,n,o;"api"!==l.select.style()&&!1!==l.select.info()&&(o=(n=(c=l.settings()[0])._select_set.length)||l.rows({selected:!0}).count(),t=l.columns({selected:!0}).count(),s=l.cells({selected:!0}).count(),"subtractive"===c._select_mode&&(o=l.page.info().recordsDisplay-n),c=function(e,t,s){e.append(m('<span class="select-item"/>').append(l.i18n("select."+t+"s",{_:"%d "+t+"s selected",0:"",1:"1 "+t+" selected"},s)))},n=m(e),c(e=m('<span class="select-info"/>'),"row",o),c(e,"column",t),c(e,"cell",s),(o=n.children("span.select-info")).length&&o.remove(),""!==e.text())&&n.append(e)}function u(e){var t=e.page.info();t.page<t.pages-1&&e.one("draw",function(){e.row(":first-child").node().focus()}).page("next").draw(!1)}function f(e){0<e.page.info().page&&e.one("draw",function(){e.row(":last-child").node().focus()}).page("previous").draw(!1)}function o(o){var c,a=new v.Api(o);o._select_init=!0,o._select_mode="additive",o._select_set=[],o.aoRowCreatedCallback.push(function(e,t,s){var l,c,n=o.aoData[s],s=a.row(s).id();for((n._select_selected||"additive"===o._select_mode&&o._select_set.includes(s)||"subtractive"===o._select_mode&&!o._select_set.includes(s))&&(n._select_selected=!0,m(e).addClass(o._select.className).find("input."+w(!0)).prop("checked",!0)),l=0,c=o.aoColumns.length;l<c;l++)(o.aoColumns[l]._select_selected||n._selected_cells&&n._selected_cells[l])&&m(n.anCells[l]).addClass(o._select.className)}),(c=a).on("select",function(e,t,s,l){"row"===s&&("additive"===(s=c.settings()[0])._select_mode?y:x)(c,s._select_set,l)}),c.on("deselect",function(e,t,s,l){"row"===s&&("additive"===(s=c.settings()[0])._select_mode?x:y)(c,s._select_set,l)}),a.on("info.dt",function(e,t,s){t._select.infoEls.includes(s)||t._select.infoEls.push(s),l(a,s)}),a.on("select.dtSelect.dt deselect.dtSelect.dt",function(){o._select.infoEls.forEach(function(e){l(a,e)}),a.state.save()}),a.on("destroy.dtSelect",function(){m(a.rows({selected:!0}).nodes()).removeClass(a.settings()[0]._select.className),m("input."+w(!0),a.table().header()).remove(),n(a),a.off(".dtSelect"),m("body").off(".dtSelect"+b(a.table().node()))})}function h(e,t,s,l){var c,n=e[t+"s"]({search:"applied"}).indexes(),l=n.indexOf(l),o=n.indexOf(s);e[t+"s"]({selected:!0}).any()||-1!==l?(o<l&&(c=o,o=l,l=c),n.splice(o+1,n.length),n.splice(0,l)):n.splice(n.indexOf(s)+1,n.length),e[t](s,{selected:!0}).any()?(n.splice(n.indexOf(s),1),e[t+"s"](n).deselect()):e[t+"s"](n).select()}function _(e,t){!t&&"single"!==e._select.style||((t=new v.Api(e)).rows({selected:!0}).deselect(),t.columns({selected:!0}).deselect(),t.cells({selected:!0}).deselect())}function p(e,t,s,l,c){var n=t.select.style(),o=t.select.toggleable(),a=t[l](c,{selected:!0}).any();a&&!o||("os"===n?e.ctrlKey||e.metaKey?t[l](c).select(!a):e.shiftKey?"cell"===l?r(t,c,s._select_lastCell||null):h(t,l,c,s._select_lastCell?s._select_lastCell[l]:null):(o=t[l+"s"]({selected:!0}),a&&1===o.flatten().length?t[l](c).deselect():(o.deselect(),t[l](c).select())):"multi+shift"==n&&e.shiftKey?"cell"===l?r(t,c,s._select_lastCell||null):h(t,l,c,s._select_lastCell?s._select_lastCell[l]:null):t[l](c).select(!a))}function b(e){return e.id.replace(/[^a-zA-Z0-9\-\_]/g,"-")}function y(e,t,s){for(var l=0;l<s.length;l++){var c=e.row(s[l]).id();c&&"undefined"!==c&&!t.includes(c)&&t.push(c)}}function x(e,t,s){for(var l=0;l<s.length;l++){var c=e.row(s[l]).id(),c=t.indexOf(c);-1!==c&&t.splice(c,1)}}v.select={},v.select.classes={checkbox:"dt-select-checkbox"},v.select.version="3.0.1",v.select.init=function(a){var e,t,s,l,c,n,o,i,r,d,u,f,h,_,p=a.settings()[0];if(!v.versionCheck("2"))throw"Warning: Select requires DataTables 2 or newer";!p._select&&(e=a.state.loaded(),t=function(e,t,s){if(null!==s&&void 0!==s.select){if(a.rows({selected:!0}).any()&&a.rows().deselect(),void 0!==s.select.rows&&a.rows(s.select.rows).select(),a.columns({selected:!0}).any()&&a.columns().deselect(),void 0!==s.select.columns&&a.columns(s.select.columns).select(),a.cells({selected:!0}).any()&&a.cells().deselect(),void 0!==s.select.cells)for(var l=0;l<s.select.cells.length;l++)a.cell(s.select.cells[l].row,s.select.cells[l].column).select();a.state.save()}},a.on("stateSaveParams",function(e,t,s){s.select={},s.select.rows=a.rows({selected:!0}).ids(!0).toArray(),s.select.columns=a.columns({selected:!0})[0],s.select.cells=a.cells({selected:!0})[0].map(function(e){return{row:a.row(e.row).id(!0),column:e.column}})}).on("stateLoadParams",t).one("init",function(){t(0,0,e)}),l=p.oInit.select,s=v.defaults.select,s=void 0===l?s:l,l="row",o=!(n=!(c="api")),d="td, th",u="selected",_=h=!(f=r=!(i=null)),p._select={infoEls:[]},!0===s?(c="os",h=!0):"string"==typeof s?(c=s,h=!0):m.isPlainObject(s)&&(void 0!==s.blurable&&(n=s.blurable),void 0!==s.toggleable&&(o=s.toggleable),void 0!==s.info&&(r=s.info),void 0!==s.items&&(l=s.items),h=(c=void 0!==s.style?s.style:"os",!0),void 0!==s.selector&&(d=s.selector),void 0!==s.className&&(u=s.className),void 0!==s.headerCheckbox&&(f=s.headerCheckbox),void 0!==s.selectable&&(i=s.selectable),void 0!==s.keys)&&(_=s.keys),a.select.selector(d),a.select.items(l),a.select.style(c),a.select.blurable(n),a.select.toggleable(o),a.select.info(r),a.select.keys(_),a.select.selectable(i),p._select.className=u,!h&&m(a.table().node()).hasClass("selectable")&&a.select.style("os"),f||"select-page"===f||"select-all"===f)&&a.ready(function(){var c,n,o;n=f,o=(c=a).settings()[0].aoColumns,c.columns().iterator("column",function(e,t){var s,l;g(o[t])&&(t=c.column(t).header(),(s=m("div.dt-column-header",t)).length&&(t=s),m("input",t).length||(l=m("<input>").attr({class:w(!0),type:"checkbox","aria-label":c.i18n("select.aria.headerCheckbox")||"Select all rows"}).appendTo(t).on("change",function(){this.checked?("select-page"==n?c.rows({page:"current"}):c.rows({search:"applied"})).select():("select-page"==n?c.rows({page:"current",selected:!0}):c.rows({selected:!0})).deselect()}).on("click",function(e){e.stopPropagation()}),c.on("draw select deselect",function(e,t,s){"row"!==s&&s||((s=((e,t)=>{var s=e.settings()[0],l=s._select.selectable,c=0,n=("select-page"==t?e.rows({page:"current",selected:!0}):e.rows({selected:!0})).count(),o=("select-page"==t?e.rows({page:"current",selected:!0}):e.rows({search:"applied",selected:!0})).count();if(l)for(var a=("select-page"==t?e.rows({page:"current"}):e.rows({search:"applied"})).indexes(),i=0;i<a.length;i++){var r=s.aoData[a[i]];l(r._aData,r.nTr,a[i])&&c++}else c=("select-page"==t?e.rows({page:"current"}):e.rows({search:"applied"})).count();return{available:c,count:n,search:o}})(c,n)).search&&s.search<=s.count&&s.search===s.available?l.prop("checked",!0).prop("indeterminate",!1):0===s.search&&0===s.count?l.prop("checked",!1).prop("indeterminate",!1):l.prop("checked",!1).prop("indeterminate",!0))})))})})},m.each([{type:"row",prop:"aoData"},{type:"column",prop:"aoColumns"}],function(e,i){v.ext.selector[i.type].push(function(e,t,s){var l,c=t.selected,n=[];if(!0!==c&&!1!==c)return s;for(var o=0,a=s.length;o<a;o++)(l=e[i.prop][s[o]])&&(!0===c&&!0===l._select_selected||!1===c&&!l._select_selected)&&n.push(s[o]);return n})}),v.ext.selector.cell.push(function(e,t,s){var l,c=t.selected,n=[];if(void 0===c)return s;for(var o=0,a=s.length;o<a;o++)(l=e.aoData[s[o].row])&&(!0===c&&l._selected_cells&&!0===l._selected_cells[s[o].column]||!1===c&&(!l._selected_cells||!l._selected_cells[s[o].column]))&&n.push(s[o]);return n});var e=v.Api.register,t=v.Api.registerPlural;function s(t,s){return function(e){return e.i18n("buttons."+t,s)}}function C(e){e=e._eventNamespace;return"draw.dt.DT"+e+" select.dt.DT"+e+" deselect.dt.DT"+e}e("select()",function(){return this.iterator("table",function(e){v.select.init(new v.Api(e))})}),e("select.blurable()",function(t){return void 0===t?this.context[0]._select.blurable:this.iterator("table",function(e){e._select.blurable=t})}),e("select.toggleable()",function(t){return void 0===t?this.context[0]._select.toggleable:this.iterator("table",function(e){e._select.toggleable=t})}),e("select.info()",function(t){return void 0===t?this.context[0]._select.info:this.iterator("table",function(e){e._select.info=t})}),e("select.items()",function(t){return void 0===t?this.context[0]._select.items:this.iterator("table",function(e){e._select.items=t,d(new v.Api(e),"selectItems",[t])})}),e("select.keys()",function(s){return void 0===s?this.context[0]._select.keys:this.iterator("table",function(e){var o,t;e._select||v.select.init(new v.Api(e)),e._select.keys=s,o=new v.Api(e),t=(e=o.settings()[0])._select.keys,e="dts-keys-"+e.sTableId,t?(m(o.rows({page:"current"}).nodes()).attr("tabindex",0),o.on("draw."+e,function(){m(o.rows({page:"current"}).nodes()).attr("tabindex",0)}),m(a).on("keydown."+e,function(e){var t,s,l,c=e.keyCode,n=a.activeElement;[9,13,32,38,40].includes(c)&&(l=!0,-1!==(s=(t=o.rows({page:"current"}).nodes().toArray()).indexOf(n)))&&(9===c?!1===e.shift&&s===t.length-1?u(o):!0===e.shift&&0===s?f(o):l=!1:13===c||32===c?(n=o.row(n)).selected()?n.deselect():n.select():38===c?0<s?t[s-1].focus():f(o):s<t.length-1?t[s+1].focus():u(o),l)&&(e.stopPropagation(),e.preventDefault())})):(m(o.rows().nodes()).removeAttr("tabindex"),o.off("draw."+e),m(a).off("keydown."+e))})}),e("select.style()",function(s){return void 0===s?this.context[0]._select.style:this.iterator("table",function(e){e._select||v.select.init(new v.Api(e)),e._select_init||o(e),e._select.style=s;var t=new v.Api(e);"api"!==s?t.ready(function(){n(t),c(t)}):n(t),d(new v.Api(e),"selectStyle",[s])})}),e("select.selector()",function(l){return void 0===l?this.context[0]._select.selector:this.iterator("table",function(e){var t=new v.Api(e),s=e._select.style;n(t),e._select.selector=l,s&&"api"!==s?t.ready(function(){n(t),c(t)}):n(t)})}),e("select.selectable()",function(e){var t=this.context[0];return e?(t._select.selectable=e,this):t._select.selectable}),e("select.last()",function(e){var t=this.context[0];return e?(t._select_lastCell=e,this):t._select_lastCell}),e("select.cumulative()",function(l){var e;return l?this.iterator("table",function(e){var t,s;e._select_mode!==l&&(t=new v.Api(e),"subtractive"===l?(s=t.rows({selected:!1}).ids().toArray(),e._select_mode=l,e._select_set.length=0,e._select_set.push.apply(e._select_set,s)):(s=t.rows({selected:!0}).ids().toArray(),e._select_mode=l,e._select_set.length=0,e._select_set.push.apply(e._select_set,s)))}).draw(!1):(e=this.context[0])&&e._select_set?{mode:e._select_mode,rows:e._select_set}:null}),t("rows().select()","row().select()",function(e){var o=this,a=[];return!1===e?this.deselect():(this.iterator("row",function(e,t){_(e);var s=e.aoData[t],l=e.aoColumns;if(e._select.selectable&&!1===e._select.selectable(s._aData,s.nTr,t))return;m(s.nTr).addClass(e._select.className),s._select_selected=!0,a.push(t);for(var c=0;c<l.length;c++){var n=l[c];null===n.sType&&o.columns().types(),g(n)&&((n=s.anCells)&&n[c]&&m("input."+w(!0),n[c]).prop("checked",!0),null!==s._aSortData)&&(s._aSortData[c]=null)}}),this.iterator("table",function(e){d(o,"select",["row",a],!0)}),this)}),e("row().selected()",function(){var e=this.context[0];return!!(e&&this.length&&e.aoData[this[0]]&&e.aoData[this[0]]._select_selected)}),e("row().focus()",function(){var e=this.context[0];e&&this.length&&e.aoData[this[0]]&&e.aoData[this[0]].nTr&&e.aoData[this[0]].nTr.focus()}),e("row().blur()",function(){var e=this.context[0];e&&this.length&&e.aoData[this[0]]&&e.aoData[this[0]].nTr&&e.aoData[this[0]].nTr.blur()}),t("columns().select()","column().select()",function(e){var s=this;return!1===e?this.deselect():(this.iterator("column",function(e,t){_(e),e.aoColumns[t]._select_selected=!0;t=new v.Api(e).column(t);m(t.header()).addClass(e._select.className),m(t.footer()).addClass(e._select.className),t.nodes().to$().addClass(e._select.className)}),this.iterator("table",function(e,t){d(s,"select",["column",s[t]],!0)}),this)}),e("column().selected()",function(){var e=this.context[0];return!!(e&&this.length&&e.aoColumns[this[0]]&&e.aoColumns[this[0]]._select_selected)}),t("cells().select()","cell().select()",function(e){var s=this;return!1===e?this.deselect():(this.iterator("cell",function(e,t,s){_(e);t=e.aoData[t];void 0===t._selected_cells&&(t._selected_cells=[]),t._selected_cells[s]=!0,t.anCells&&m(t.anCells[s]).addClass(e._select.className)}),this.iterator("table",function(e,t){d(s,"select",["cell",s.cells(s[t]).indexes().toArray()],!0)}),this)}),e("cell().selected()",function(){var e=this.context[0];if(e&&this.length){e=e.aoData[this[0][0].row];if(e&&e._selected_cells&&e._selected_cells[this[0][0].column])return!0}return!1}),t("rows().deselect()","row().deselect()",function(){var o=this;return this.iterator("row",function(e,t){var s=e.aoData[t],l=e.aoColumns;m(s.nTr).removeClass(e._select.className),s._select_selected=!1,e._select_lastCell=null;for(var c=0;c<l.length;c++){var n=l[c];null===n.sType&&o.columns().types(),g(n)&&((n=s.anCells)&&n[c]&&m("input."+w(!0),s.anCells[c]).prop("checked",!1),null!==s._aSortData)&&(s._aSortData[c]=null)}}),this.iterator("table",function(e,t){d(o,"deselect",["row",o[t]],!0)}),this}),t("columns().deselect()","column().deselect()",function(){var s=this;return this.iterator("column",function(l,e){l.aoColumns[e]._select_selected=!1;var t=new v.Api(l),s=t.column(e);m(s.header()).removeClass(l._select.className),m(s.footer()).removeClass(l._select.className),t.cells(null,e).indexes().each(function(e){var t=l.aoData[e.row],s=t._selected_cells;!t.anCells||s&&s[e.column]||m(t.anCells[e.column]).removeClass(l._select.className)})}),this.iterator("table",function(e,t){d(s,"deselect",["column",s[t]],!0)}),this}),t("cells().deselect()","cell().deselect()",function(){var s=this;return this.iterator("cell",function(e,t,s){t=e.aoData[t];void 0!==t._selected_cells&&(t._selected_cells[s]=!1),t.anCells&&!e.aoColumns[s]._select_selected&&m(t.anCells[s]).removeClass(e._select.className)}),this.iterator("table",function(e,t){d(s,"deselect",["cell",s[t]],!0)}),this});var k=0;return m.extend(v.ext.buttons,{selected:{text:s("selected","Selected"),className:"buttons-selected",limitTo:["rows","columns","cells"],init:function(s,e,l){var c=this;l._eventNamespace=".select"+k++,s.on(C(l),function(){var e,t;c.enable((e=s,!(-1===(t=l).limitTo.indexOf("rows")||!e.rows({selected:!0}).any())||!(-1===t.limitTo.indexOf("columns")||!e.columns({selected:!0}).any())||!(-1===t.limitTo.indexOf("cells")||!e.cells({selected:!0}).any())))}),this.disable()},destroy:function(e,t,s){e.off(s._eventNamespace)}},selectedSingle:{text:s("selectedSingle","Selected single"),className:"buttons-selected-single",init:function(t,e,s){var l=this;s._eventNamespace=".select"+k++,t.on(C(s),function(){var e=t.rows({selected:!0}).flatten().length+t.columns({selected:!0}).flatten().length+t.cells({selected:!0}).flatten().length;l.enable(1===e)}),this.disable()},destroy:function(e,t,s){e.off(s._eventNamespace)}},selectAll:{text:s("selectAll","Select all"),className:"buttons-select-all",action:function(e,t,s,l){var c=this.select.items(),n=l.selectorModifier;(n?("function"==typeof n&&(n=n.call(t,e,t,s,l)),this[c+"s"](n)):this[c+"s"]()).select()}},selectNone:{text:s("selectNone","Deselect all"),className:"buttons-select-none",action:function(){_(this.settings()[0],!0)},init:function(t,e,s){var l=this;s._eventNamespace=".select"+k++,t.on(C(s),function(){var e=t.rows({selected:!0}).flatten().length+t.columns({selected:!0}).flatten().length+t.cells({selected:!0}).flatten().length;l.enable(0<e)}),this.disable()},destroy:function(e,t,s){e.off(s._eventNamespace)}},showSelected:{text:s("showSelected","Show only selected"),className:"buttons-show-selected",action:function(e,t){var l;t.search.fixed("dt-select")?(t.search.fixed("dt-select",null),this.active(!1)):(l=t.settings()[0].aoData,t.search.fixed("dt-select",function(e,t,s){return l[s]._select_selected}),this.active(!0)),t.draw()}}}),m.each(["Row","Column","Cell"],function(e,t){var c=t.toLowerCase();v.ext.buttons["select"+t+"s"]={text:s("select"+t+"s","Select "+c+"s"),className:"buttons-select-"+c+"s",action:function(){this.select.items(c)},init:function(e){var l=this;this.active(e.select.items()===c),e.on("selectItems.dt.DT",function(e,t,s){l.active(s===c)})}}}),v.type("select-checkbox",{className:"dt-select",detect:v.versionCheck("2.1")?{oneOf:function(){return!1},allOf:function(){return!1},init:function(e,t,s){return g(t)}}:function(e){return"select-checkbox"===e&&e},order:{pre:function(e){return"X"===e?-1:0}}}),m.extend(!0,v.defaults.oLanguage,{select:{aria:{rowCheckbox:"Select row"}}}),v.render.select=function(e,t){function s(e,t,s,l){var c=l.settings.aoData[l.row],n=c._select_selected,o=l.settings.oLanguage.select.aria.rowCheckbox,a=l.settings._select.selectable;return"display"!==t?"type"===t?"select-checkbox":"filter"!==t&&n?"X":"":a&&!1===a(s,c.nTr,l.row)?"":m("<input>").attr({"aria-label":o,class:w(),name:r?r(s):null,type:"checkbox",value:i?i(s):null,checked:n}).on("input",function(e){e.preventDefault(),this.checked=m(this).closest("tr").hasClass("selected")})[0]}var i=e?v.util.get(e):null,r=t?v.util.get(t):null;return s._name="selectCheckbox",s},v.ext.order["select-checkbox"]=function(t,e){return this.api().column(e,{order:"index"}).nodes().map(function(e){return"row"===t._select.items?m(e).parent().hasClass(t._select.className).toString():"cell"===t._select.items&&m(e).hasClass(t._select.className).toString()})},m.fn.DataTable.select=v.select,m(a).on("i18n.dt.dtSelect preInit.dt.dtSelect",function(e,t){"dt"===e.namespace&&v.select.init(new v.Api(t))}),v});