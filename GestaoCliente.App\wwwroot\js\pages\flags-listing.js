/**
 * Template Name: Simple - Responsive Admin & Dashboard Template
 * By (Author): Coderthemes
 * Module/App (File Name): Flag Listing
 * Version: 3.0.0
 */

const countries = [
    {name: "Aruba", code: "aw"},
    {name: "Afghanistan", code: "af"},
    {name: "Angola", code: "ao"},
    {name: "Anguil<PERSON>", code: "ai"},
    {name: "Åland Islands", code: "ax"},
    {name: "Albania", code: "al"},
    {name: "Andorra", code: "ad"},
    {name: "United Arab Emirates", code: "ae"},
    {name: "Argentina", code: "ar"},
    {name: "Armenia", code: "am"},
    {name: "American Samoa", code: "as"},
    {name: "Antarctica", code: "aq"},
    {name: "French Southern Territories", code: "tf"},
    {name: "Antigua and Barbuda", code: "ag"},
    {name: "Australia", code: "au"},
    {name: "Austria", code: "at"},
    {name: "Azerbaijan", code: "az"},
    {name: "Burundi", code: "bi"},
    {name: "Belgium", code: "be"},
    {name: "Benin", code: "bj"},
    {name: "Bonaire, Sint Eustatius and Saba", code: "bq"},
    {name: "Burkina Faso", code: "bf"},
    {name: "Bangladesh", code: "bd"},
    {name: "Bulgaria", code: "bg"},
    {name: "Bahrain", code: "bh"},
    {name: "Bahamas", code: "bs"},
    {name: "Bosnia and Herzegovina", code: "ba"},
    {name: "Saint Barthélemy", code: "bl"},
    {name: "Belarus", code: "by"},
    {name: "Belize", code: "bz"},
    {name: "Bermuda", code: "bm"},
    {name: "Bolivia, Plurinational State of", code: "bo"},
    {name: "Brazil", code: "br"},
    {name: "Barbados", code: "bb"},
    {name: "Brunei Darussalam", code: "bn"},
    {name: "Bhutan", code: "bt"},
    {name: "Bouvet Island", code: "bv"},
    {name: "Botswana", code: "bw"},
    {name: "Central African Republic", code: "cf"},
    {name: "Canada", code: "ca"},
    {name: "Cocos (Keeling) Islands", code: "cc"},
    {name: "Switzerland", code: "ch"},
    {name: "Chile", code: "cl"},
    {name: "China", code: "cn"},
    {name: "Côte d'Ivoire", code: "ci"},
    {name: "Cameroon", code: "cm"},
    {name: "Congo, The Democratic Republic of the", code: "cd"},
    {name: "Congo", code: "cg"},
    {name: "Cook Islands", code: "ck"},
    {name: "Colombia", code: "co"},
    {name: "Comoros", code: "km"},
    {name: "Cabo Verde", code: "cv"},
    {name: "Costa Rica", code: "cr"},
    {name: "Cuba", code: "cu"},
    {name: "Curaçao", code: "cw"},
    {name: "Christmas Island", code: "cx"},
    {name: "Cayman Islands", code: "ky"},
    {name: "Cyprus", code: "cy"},
    {name: "Czechia", code: "cz"},
    {name: "Germany", code: "de"},
    {name: "Djibouti", code: "dj"},
    {name: "Dominica", code: "dm"},
    {name: "Denmark", code: "dk"},
    {name: "Dominican Republic", code: "do"},
    {name: "Algeria", code: "dz"},
    {name: "Ecuador", code: "ec"},
    {name: "Egypt", code: "eg"},
    {name: "Eritrea", code: "er"},
    {name: "Western Sahara", code: "eh"},
    {name: "Spain", code: "es"},
    {name: "Estonia", code: "ee"},
    {name: "Ethiopia", code: "et"},
    {name: "Finland", code: "fi"},
    {name: "Fiji", code: "fj"},
    {name: "Falkland Islands (Malvinas)", code: "fk"},
    {name: "France", code: "fr"},
    {name: "Faroe Islands", code: "fo"},
    {name: "Micronesia, Federated States of", code: "fm"},
    {name: "Gabon", code: "ga"},
    {name: "United Kingdom", code: "gb"},
    {name: "Georgia", code: "ge"},
    {name: "Guernsey", code: "gg"},
    {name: "Ghana", code: "gh"},
    {name: "Gibraltar", code: "gi"},
    {name: "Guinea", code: "gn"},
    {name: "Guadeloupe", code: "gp"},
    {name: "Gambia", code: "gm"},
    {name: "Guinea-Bissau", code: "gw"},
    {name: "Equatorial Guinea", code: "gq"},
    {name: "Greece", code: "gr"},
    {name: "Grenada", code: "gd"},
    {name: "Greenland", code: "gl"},
    {name: "Guatemala", code: "gt"},
    {name: "French Guiana", code: "gf"},
    {name: "Guam", code: "gu"},
    {name: "Guyana", code: "gy"},
    {name: "Hong Kong", code: "hk"},
    {name: "Heard Island and McDonald Islands", code: "hm"},
    {name: "Honduras", code: "hn"},
    {name: "Croatia", code: "hr"},
    {name: "Haiti", code: "ht"},
    {name: "Hungary", code: "hu"},
    {name: "Indonesia", code: "id"},
    {name: "Isle of Man", code: "im"},
    {name: "India", code: "in"},
    {name: "British Indian Ocean Territory", code: "io"},
    {name: "Ireland", code: "ie"},
    {name: "Iran, Islamic Republic of", code: "ir"},
    {name: "Iraq", code: "iq"},
    {name: "Iceland", code: "is"},
    {name: "Israel", code: "il"},
    {name: "Italy", code: "it"},
    {name: "Jamaica", code: "jm"},
    {name: "Jersey", code: "je"},
    {name: "Jordan", code: "jo"},
    {name: "Japan", code: "jp"},
    {name: "Kazakhstan", code: "kz"},
    {name: "Kenya", code: "ke"},
    {name: "Kyrgyzstan", code: "kg"},
    {name: "Cambodia", code: "kh"},
    {name: "Kiribati", code: "ki"},
    {name: "Saint Kitts and Nevis", code: "kn"},
    {name: "Korea, Republic of", code: "kr"},
    {name: "Kuwait", code: "kw"},
    {name: "Lao People's Democratic Republic", code: "la"},
    {name: "Lebanon", code: "lb"},
    {name: "Liberia", code: "lr"},
    {name: "Libya", code: "ly"},
    {name: "Saint Lucia", code: "lc"},
    {name: "Liechtenstein", code: "li"},
    {name: "Sri Lanka", code: "lk"},
    {name: "Lesotho", code: "ls"},
    {name: "Lithuania", code: "lt"},
    {name: "Luxembourg", code: "lu"},
    {name: "Latvia", code: "lv"},
    {name: "Macao", code: "mo"},
    {name: "Saint Martin (French part)", code: "mf"},
    {name: "Morocco", code: "ma"},
    {name: "Monaco", code: "mc"},
    {name: "Moldova, Republic of", code: "md"},
    {name: "Madagascar", code: "mg"},
    {name: "Maldives", code: "mv"},
    {name: "Mexico", code: "mx"},
    {name: "Marshall Islands", code: "mh"},
    {name: "North Macedonia", code: "mk"},
    {name: "Mali", code: "ml"},
    {name: "Malta", code: "mt"},
    {name: "Myanmar", code: "mm"},
    {name: "Montenegro", code: "me"},
    {name: "Mongolia", code: "mn"},
    {name: "Northern Mariana Islands", code: "mp"},
    {name: "Mozambique", code: "mz"},
    {name: "Mauritania", code: "mr"},
    {name: "Montserrat", code: "ms"},
    {name: "Martinique", code: "mq"},
    {name: "Mauritius", code: "mu"},
    {name: "Malawi", code: "mw"},
    {name: "Malaysia", code: "my"},
    {name: "Mayotte", code: "yt"},
    {name: "Namibia", code: "na"},
    {name: "New Caledonia", code: "nc"},
    {name: "Niger", code: "ne"},
    {name: "Norfolk Island", code: "nf"},
    {name: "Nigeria", code: "ng"},
    {name: "Nicaragua", code: "ni"},
    {name: "Niue", code: "nu"},
    {name: "Netherlands", code: "nl"},
    {name: "Norway", code: "no"},
    {name: "Nepal", code: "np"},
    {name: "Nauru", code: "nr"},
    {name: "New Zealand", code: "nz"},
    {name: "Oman", code: "om"},
    // { name: "Pakistan", code: "pk" },
    {name: "Panama", code: "pa"},
    {name: "Pitcairn", code: "pn"},
    {name: "Peru", code: "pe"},
    {name: "Philippines", code: "ph"},
    {name: "Palau", code: "pw"},
    {name: "Papua New Guinea", code: "pg"},
    {name: "Poland", code: "pl"},
    {name: "Puerto Rico", code: "pr"},
    {name: "Korea, Democratic People's Republic of", code: "kp"},
    {name: "Portugal", code: "pt"},
    {name: "Paraguay", code: "py"},
    {name: "Palestine, State of", code: "ps"},
    {name: "French Polynesia", code: "pf"},
    {name: "Qatar", code: "qa"},
    {name: "Réunion", code: "re"},
    {name: "Romania", code: "ro"},
    {name: "Russian Federation", code: "ru"},
    {name: "Rwanda", code: "rw"},
    {name: "Saudi Arabia", code: "sa"},
    {name: "Sudan", code: "sd"},
    {name: "Senegal", code: "sn"},
    {name: "Singapore", code: "sg"},
    {name: "South Georgia and the South Sandwich Islands", code: "gs"},
    {name: "Saint Helena, Ascension and Tristan da Cunha", code: "sh"},
    {name: "Svalbard and Jan Mayen", code: "sj"},
    {name: "Solomon Islands", code: "sb"},
    {name: "Sierra Leone", code: "sl"},
    {name: "El Salvador", code: "sv"},
    {name: "San Marino", code: "sm"},
    {name: "Somalia", code: "so"},
    {name: "Saint Pierre and Miquelon", code: "pm"},
    {name: "Serbia", code: "rs"},
    {name: "South Sudan", code: "ss"},
    {name: "Sao Tome and Principe", code: "st"},
    {name: "Suriname", code: "sr"},
    {name: "Slovakia", code: "sk"},
    {name: "Slovenia", code: "si"},
    {name: "Sweden", code: "se"},
    {name: "Eswatini", code: "sz"},
    {name: "Sint Maarten (Dutch part)", code: "sx"},
    {name: "Seychelles", code: "sc"},
    {name: "Syrian Arab Republic", code: "sy"},
    {name: "Turks and Caicos Islands", code: "tc"},
    {name: "Chad", code: "td"},
    {name: "Togo", code: "tg"},
    {name: "Thailand", code: "th"},
    {name: "Tajikistan", code: "tj"},
    {name: "Tokelau", code: "tk"},
    {name: "Turkmenistan", code: "tm"},
    {name: "Timor-Leste", code: "tl"},
    {name: "Tonga", code: "to"},
    {name: "Trinidad and Tobago", code: "tt"},
    {name: "Tunisia", code: "tn"},
    {name: "Turkey", code: "tr"},
    {name: "Tuvalu", code: "tv"},
    {name: "Taiwan, Province of China", code: "tw"},
    {name: "Tanzania, United Republic of", code: "tz"},
    {name: "Uganda", code: "ug"},
    {name: "Ukraine", code: "ua"},
    {name: "United States Minor Outlying Islands", code: "um"},
    {name: "Uruguay", code: "uy"},
    {name: "United States", code: "us"},
    {name: "Uzbekistan", code: "uz"},
    {name: "Holy See (Vatican City State)", code: "va"},
    {name: "Saint Vincent and the Grenadines", code: "vc"},
    {name: "Venezuela, Bolivarian Republic of", code: "ve"},
    {name: "Virgin Islands, British", code: "vg"},
    {name: "Virgin Islands, U.S.", code: "vi"},
    {name: "Viet Nam", code: "vn"},
    {name: "Vanuatu", code: "vu"},
    {name: "Wallis and Futuna", code: "wf"},
    {name: "Samoa", code: "ws"},
    {name: "Yemen", code: "ye"},
    {name: "South Africa", code: "za"},
    {name: "Zambia", code: "zm"},
    {name: "Zimbabwe", code: "zw"}
];

const tableBody = document.querySelector("#flagTable tbody");
const searchInput = document.querySelector("#countrySearch");

if (tableBody && searchInput) {

    renderTable(countries);

    // Search functionality
    searchInput.addEventListener("input", function () {
        const searchTerm = searchInput.value.toLowerCase();
        const filteredCountries = countries.filter(country =>
            country.name.toLowerCase().includes(searchTerm)
        );
        renderTable(filteredCountries); // Re-render table with filtered countries
    });
}

function renderTable(countriesList) {
    tableBody.innerHTML = "";

    if (!Array.isArray(countriesList) || countriesList.length === 0) {
        const alertRow = document.createElement("tr");
        alertRow.innerHTML = `
            <td colspan="6" class="text-center table-danger" role="alert">
                No country name found. Please try again with a different search.
            </td>
        `;
        tableBody.appendChild(alertRow);
        return;
    }

    for (let i = 0; i < countriesList.length; i += 2) {
        const row = document.createElement("tr");

        for (let j = 0; j < 2; j++) {
            const country = countriesList[i + j];

            if (country && country.code && country.name) {
                const code = country.code.toLowerCase().trim();
                const imgPath = `/images/flags/${code}.svg`;

                row.innerHTML += `
                    <td>
                        <img src="${imgPath}" alt="${country.name}" height="18" class="rounded" 
                            onerror="this.onerror=null; this.src='/images/flags/unknown.svg'; this.alt='Not Found'; this.classList.add('border', 'border-danger');">
                    </td>
                    <td>${country.name}</td>
                    <td><code>${imgPath}</code></td>
                `;
            } else {
                row.innerHTML += `<td></td><td></td><td></td>`;
            }
        }

        tableBody.appendChild(row);
    }
}