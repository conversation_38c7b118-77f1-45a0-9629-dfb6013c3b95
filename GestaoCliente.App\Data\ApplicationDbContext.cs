using Microsoft.EntityFrameworkCore;
using GestaoCliente.App.Models;

namespace GestaoCliente.App.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Cliente> Clientes { get; set; }
        public DbSet<Cobranca> Cobranc<PERSON> { get; set; }
        public DbSet<Pagamento> Pagamentos { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configurações para Cliente
            modelBuilder.Entity<Cliente>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Nome).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Email).HasMaxLength(150);
                entity.Property(e => e.Telefone).IsRequired().HasMaxLength(20);
                entity.Property(e => e.NomeEmpresa).HasMaxLength(100);
                entity.Property(e => e.DataCriacao).HasDefaultValueSql("datetime('now')");
            });

            // Configurações para Cobranca
            modelBuilder.Entity<Cobranca>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ValorTotal).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.Parcelas).IsRequired();
                entity.Property(e => e.DataVencimento).IsRequired();
                entity.Property(e => e.DataCriacao).HasDefaultValueSql("datetime('now')");

                // Relacionamento com Cliente
                entity.HasOne(e => e.Cliente)
                      .WithMany(c => c.Cobrancas)
                      .HasForeignKey(e => e.ClienteId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Configurações para Pagamento
            modelBuilder.Entity<Pagamento>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Valor).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.Pago).IsRequired();
                entity.Property(e => e.DataVencimento).IsRequired();
                entity.Property(e => e.DataCriacao).HasDefaultValueSql("datetime('now')");

                // Relacionamento com Cobranca
                entity.HasOne(e => e.Cobranca)
                      .WithMany(c => c.Pagamentos)
                      .HasForeignKey(e => e.CobrancaId)
                      .OnDelete(DeleteBehavior.Cascade);
            });
        }
    }
}
