using System.ComponentModel.DataAnnotations;

namespace GestaoCliente.App.Models
{
    public class Cliente
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "O nome é obrigatório")]
        [StringLength(100, ErrorMessage = "O nome deve ter no máximo 100 caracteres")]
        public string Nome { get; set; } = string.Empty;

        [EmailAddress(ErrorMessage = "Email inválido")]
        [StringLength(150, ErrorMessage = "O email deve ter no máximo 150 caracteres")]
        public string? Email { get; set; }

        [Required(ErrorMessage = "O telefone é obrigatório")]
        [StringLength(20, ErrorMessage = "O telefone deve ter no máximo 20 caracteres")]
        public string Telefone { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "O nome da empresa deve ter no máximo 100 caracteres")]
        public string? NomeEmpresa { get; set; }

        public DateTime DataCriacao { get; set; } = DateTime.Now;

        // Relacionamento com Cobrancas
        public virtual ICollection<Cobranca> Cobrancas { get; set; } = new List<Cobranca>();
    }
}
