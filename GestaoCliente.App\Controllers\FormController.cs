using Microsoft.AspNetCore.Mvc;

namespace Simple.Controllers
    {
        public class FormController : Controller
        {
            public IActionResult Validation()
        {
            return View();
        }

        public IActionResult Plugins()
        {
            return View();
        }

        public IActionResult Wizard()
        {
            return View();
        }

        public IActionResult Fileuploads()
        {
            return View();
        }

        public IActionResult Elements()
        {
            return View();
        }

        public IActionResult QuillEditor()
        {
            return View();
        }
        }
    }