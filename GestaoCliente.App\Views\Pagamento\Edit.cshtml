@model GestaoCliente.App.Models.Pagamento

@{
    ViewData["Title"] = "Editar Pagamento";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">@ViewData["Title"]</h4>
            </div>
            <div class="card-body">
                <form asp-action="Edit" method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <input type="hidden" asp-for="Id" />
                    <input type="hidden" asp-for="DataCriacao" />

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="CobrancaId" class="form-label">Cobrança</label>
                                <select asp-for="CobrancaId" class="form-select" asp-items="ViewBag.CobrancaId">
                                    <option value="">Selecione uma cobrança</option>
                                </select>
                                <span asp-validation-for="CobrancaId" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Valor" class="form-label">Valor</label>
                                <div class="input-group">
                                    <span class="input-group-text">R$</span>
                                    <input asp-for="Valor" class="form-control" step="0.01" />
                                </div>
                                <span asp-validation-for="Valor" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="DataVencimento" class="form-label">Data de Vencimento</label>
                                <input asp-for="DataVencimento" class="form-control" type="date" />
                                <span asp-validation-for="DataVencimento" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6" id="dataPagamentoRow" style="display: none;">
                            <div class="form-group mb-3">
                                <label asp-for="DataPagamento" class="form-label">Data de Pagamento</label>
                                <input asp-for="DataPagamento" class="form-control" type="date" />
                                <span asp-validation-for="DataPagamento" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="form-group mb-3">
                                <div class="form-check form-switch">
                                    <input asp-for="Pago" class="form-check-input" type="checkbox" id="pagoSwitch" />
                                    <label class="form-check-label" for="pagoSwitch">
                                        <strong>Pagamento Realizado</strong>
                                    </label>
                                </div>
                                <small class="form-text text-muted">
                                    Marque esta opção se o pagamento já foi realizado
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i data-lucide="info"></i> Informação
                                </h6>
                                <p class="mb-0">
                                    Ao marcar como pago, a data de pagamento será automaticamente definida como hoje, 
                                    caso não seja especificada uma data diferente.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i data-lucide="arrow-left"></i> Voltar
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i data-lucide="save"></i> Salvar Alterações
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const pagoSwitch = document.getElementById('pagoSwitch');
            const dataPagamentoRow = document.getElementById('dataPagamentoRow');
            const dataPagamentoInput = document.querySelector('input[name="DataPagamento"]');
            
            function toggleDataPagamento() {
                if (pagoSwitch.checked) {
                    dataPagamentoRow.style.display = 'block';
                    // Se não há data definida e está marcado como pago, definir como hoje
                    if (!dataPagamentoInput.value) {
                        const today = new Date().toISOString().split('T')[0];
                        dataPagamentoInput.value = today;
                    }
                } else {
                    dataPagamentoRow.style.display = 'none';
                    dataPagamentoInput.value = '';
                }
            }
            
            pagoSwitch.addEventListener('change', toggleDataPagamento);
            
            // Verificar estado inicial
            toggleDataPagamento();
        });
    </script>
}
