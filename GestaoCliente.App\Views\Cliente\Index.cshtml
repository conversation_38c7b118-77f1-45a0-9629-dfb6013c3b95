@model IEnumerable<GestaoCliente.App.Models.Cliente>

@{
    ViewData["Title"] = "Clientes";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="card-title mb-0">@ViewData["Title"]</h4>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Novo Cliente
                </a>
            </div>
            <div class="card-body">
                @if (TempData["Success"] != null)
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        @TempData["Success"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }

                @if (Model.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Nome</th>
                                    <th>Email</th>
                                    <th>Telefone</th>
                                    <th>Empresa</th>
                                    <th>Cobranças</th>
                                    <th>Data Criação</th>
                                    <th width="200">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <strong>@Html.DisplayFor(modelItem => item.Nome)</strong>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Email))
                                            {
                                                <a href="mailto:@item.Email">@item.Email</a>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            <a href="tel:@item.Telefone">@Html.DisplayFor(modelItem => item.Telefone)</a>
                                        </td>
                                        <td>
                                            @Html.DisplayFor(modelItem => item.NomeEmpresa)
                                            @if (string.IsNullOrEmpty(item.NomeEmpresa))
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-info">@item.Cobrancas.Count</span>
                                        </td>
                                        <td>
                                            @item.DataCriacao.ToString("dd/MM/yyyy")
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-info" title="Detalhes">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-warning" title="Editar">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-action="Delete" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-danger" title="Excluir">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum cliente cadastrado</h5>
                        <p class="text-muted">Clique no botão "Novo Cliente" para começar.</p>
                        <a asp-action="Create" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Cadastrar Primeiro Cliente
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
