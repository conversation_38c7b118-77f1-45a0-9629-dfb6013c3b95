@model IEnumerable<GestaoCliente.App.Models.Pagamento>

@{
    ViewData["Title"] = "Pagamentos";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="card-title mb-0">@ViewData["Title"]</h4>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Novo Pagamento
                </a>
            </div>
            <div class="card-body">
                @if (TempData["Success"] != null)
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        @TempData["Success"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }

                @if (Model.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Cliente</th>
                                    <th>Cobrança</th>
                                    <th>Valor</th>
                                    <th>Status</th>
                                    <th>Vencimento</th>
                                    <th>Data Pagamento</th>
                                    <th width="200">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    var isVencido = !item.Pago && item.DataVencimento < DateTime.Now;
                                    var rowClass = item.Pago ? "table-success" : isVencido ? "table-danger" : "";
                                    
                                    <tr class="@rowClass">
                                        <td>
                                            <strong>@item.Cobranca.Cliente.Nome</strong>
                                            @if (!string.IsNullOrEmpty(item.Cobranca.Cliente.NomeEmpresa))
                                            {
                                                <br><small class="text-muted">@item.Cobranca.Cliente.NomeEmpresa</small>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                R$ @item.Cobranca.ValorTotal.ToString("F2")
                                            </span>
                                            <br><small class="text-muted">@item.Cobranca.Parcelas parcelas</small>
                                        </td>
                                        <td>
                                            <strong class="text-primary">@item.Valor.ToString("C")</strong>
                                        </td>
                                        <td>
                                            @if (item.Pago)
                                            {
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check"></i> Pago
                                                </span>
                                            }
                                            else if (isVencido)
                                            {
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-exclamation-triangle"></i> Vencido
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock"></i> Pendente
                                                </span>
                                            }
                                        </td>
                                        <td>
                                            @item.DataVencimento.ToString("dd/MM/yyyy")
                                            @if (isVencido)
                                            {
                                                <br><small class="text-danger">
                                                    @((DateTime.Now - item.DataVencimento).Days) dias em atraso
                                                </small>
                                            }
                                        </td>
                                        <td>
                                            @if (item.DataPagamento.HasValue)
                                            {
                                                @item.DataPagamento.Value.ToString("dd/MM/yyyy")
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                @if (!item.Pago)
                                                {
                                                    <form asp-action="MarcarComoPago" asp-route-id="@item.Id" method="post" style="display: inline;">
                                                        <button type="submit" class="btn btn-sm btn-success" title="Marcar como Pago"
                                                                onclick="return confirm('Marcar este pagamento como pago?')">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    </form>
                                                }
                                                <a asp-action="Details" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-info" title="Detalhes">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-warning" title="Editar">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-action="Delete" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-danger" title="Excluir">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum pagamento cadastrado</h5>
                        <p class="text-muted">Os pagamentos são criados automaticamente quando você cria uma cobrança.</p>
                        <a asp-controller="Cobranca" asp-action="Create" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Criar Nova Cobrança
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
