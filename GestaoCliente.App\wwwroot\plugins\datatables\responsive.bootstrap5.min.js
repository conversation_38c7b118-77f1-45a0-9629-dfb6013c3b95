/*! Bootstrap 5 integration for DataTables' Responsive
 * © SpryMedia Ltd - datatables.net/license
 */
!function(t){var o,n;"function"==typeof define&&define.amd?define(["jquery","datatables.net-bs5","datatables.net-responsive"],function(e){return t(e,window,document)}):"object"==typeof exports?(o=require("jquery"),n=function(e,d){d.fn.dataTable||require("datatables.net-bs5")(e,d),d.fn.dataTable.Responsive||require("datatables.net-responsive")(e,d)},"undefined"==typeof window?module.exports=function(e,d){return e=e||window,d=d||o(e),n(e,d),t(d,e,e.document)}:(n(window,o),module.exports=t(o,window,window.document))):t(jQuery,window,document)}(function(s,e,l){"use strict";var u,d=s.fn.dataTable,t=d.Responsive.display,f=t.modal,p=s('<div class="modal fade dtr-bs-modal" role="dialog"><div class="modal-dialog" role="document"><div class="modal-content"><div class="modal-header"><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"/></div></div></div>'),o=e.bootstrap;return d.Responsive.bootstrap=function(e){o=e},t.modal=function(r){var e;return!u&&o.Modal&&(e=function(){var e=d.use("bootstrap");if(e)return e;if(o)return o;throw new Error("No Bootstrap library. Set it with `DataTable.use(bootstrap);`")}(),u=new e.Modal(p[0])),function(e,d,t,o){if(u){var n,a,i=t();if(!1===i)return!1;if(d){if(!s.contains(l,p[0])||e.index()!==p.data("dtr-row-idx"))return null;p.find("div.modal-body").empty().append(i)}else r&&r.header&&(a=(n=p.find("div.modal-header")).find("button").detach(),n.empty().append('<h4 class="modal-title">'+r.header(e)+"</h4>").append(a)),p.find("div.modal-body").empty().append(i),p.data("dtr-row-idx",e.index()).one("hidden.bs.modal",o).appendTo("body"),u.show();return!0}return f(e,d,t,o)}},d});