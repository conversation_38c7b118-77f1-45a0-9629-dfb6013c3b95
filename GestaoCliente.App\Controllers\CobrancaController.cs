using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using GestaoCliente.App.Data;
using GestaoCliente.App.Models;

namespace GestaoCliente.App.Controllers
{
    public class CobrancaController : Controller
    {
        private readonly ApplicationDbContext _context;

        public CobrancaController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Cobranca
        public async Task<IActionResult> Index()
        {
            var cobrancas = await _context.Cobrancas
                .Include(c => c.Cliente)
                .Include(c => c.Pagamentos)
                .OrderByDescending(c => c.DataCriacao)
                .ToListAsync();
            return View(cobrancas);
        }

        // GET: Cobranca/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var cobranca = await _context.Cobrancas
                .Include(c => c.Cliente)
                .Include(c => c.<PERSON>)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (cobranca == null)
            {
                return NotFound();
            }

            return View(cobranca);
        }

        // GET: Cobranca/Create
        public IActionResult Create()
        {
            ViewData["ClienteId"] = new SelectList(_context.Clientes.OrderBy(c => c.Nome), "Id", "Nome");
            return View();
        }

        // POST: Cobranca/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("ClienteId,ValorTotal,Parcelas,DataVencimento")] Cobranca cobranca)
        {
            ModelState.Remove("Cliente");
            if (ModelState.IsValid)
            {
                cobranca.DataCriacao = DateTime.Now;
                _context.Add(cobranca);
                await _context.SaveChangesAsync();

                // Criar pagamentos automaticamente baseado no número de parcelas
                await CriarPagamentosAutomaticamente(cobranca);

                TempData["Success"] = "Cobrança criada com sucesso!";
                return RedirectToAction(nameof(Index));
            }
            ViewData["ClienteId"] = new SelectList(_context.Clientes.OrderBy(c => c.Nome), "Id", "Nome", cobranca.ClienteId);
            return View(cobranca);
        }

        // GET: Cobranca/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var cobranca = await _context.Cobrancas.FindAsync(id);
            if (cobranca == null)
            {
                return NotFound();
            }
            ViewData["ClienteId"] = new SelectList(_context.Clientes.OrderBy(c => c.Nome), "Id", "Nome", cobranca.ClienteId);
            return View(cobranca);
        }

        // POST: Cobranca/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,ClienteId,ValorTotal,Parcelas,DataVencimento,DataCriacao")] Cobranca cobranca)
        {
            if (id != cobranca.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(cobranca);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = "Cobrança atualizada com sucesso!";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CobrancaExists(cobranca.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["ClienteId"] = new SelectList(_context.Clientes.OrderBy(c => c.Nome), "Id", "Nome", cobranca.ClienteId);
            return View(cobranca);
        }

        // GET: Cobranca/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var cobranca = await _context.Cobrancas
                .Include(c => c.Cliente)
                .Include(c => c.Pagamentos)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (cobranca == null)
            {
                return NotFound();
            }

            return View(cobranca);
        }

        // POST: Cobranca/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var cobranca = await _context.Cobrancas.FindAsync(id);
            if (cobranca != null)
            {
                _context.Cobrancas.Remove(cobranca);
                await _context.SaveChangesAsync();
                TempData["Success"] = "Cobrança excluída com sucesso!";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool CobrancaExists(int id)
        {
            return _context.Cobrancas.Any(e => e.Id == id);
        }

        private async Task CriarPagamentosAutomaticamente(Cobranca cobranca)
        {
            var valorParcela = cobranca.ValorTotal / cobranca.Parcelas;
            var dataVencimento = cobranca.DataVencimento;

            for (int i = 1; i <= cobranca.Parcelas; i++)
            {
                var pagamento = new Pagamento
                {
                    CobrancaId = cobranca.Id,
                    Valor = valorParcela,
                    Pago = false,
                    DataVencimento = dataVencimento.AddMonths(i - 1),
                    DataCriacao = DateTime.Now
                };

                _context.Pagamentos.Add(pagamento);
            }

            await _context.SaveChangesAsync();
        }
    }
}
