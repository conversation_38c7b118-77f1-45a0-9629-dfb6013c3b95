
@{
    ViewBag.Title = "Rich Text Editor";
    ViewBag.SubTitle = "Create and edit beautifully formatted content with Quill.js — a modern WYSIWYG editor with extensible features.";
    ViewBag.BadgeIcon = "edit";
    ViewBag.BadgeTitle = "Quill.js Editor";
}

@section styles
{
    <link href="/plugins/quill/quill.core.css" rel="stylesheet" type="text/css"/>
    <link href="/plugins/quill/quill.snow.css" rel="stylesheet" type="text/css"/>
    <link href="/plugins/quill/quill.bubble.css" rel="stylesheet" type="text/css"/>
}

<div class="container-fluid">

    @await Html.PartialAsync("~/Views/Shared/Partials/_PageTitle.cshtml")


    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title mb-1">Snow Editor</h5>
                    <p class="text-muted">Snow is a clean, flat toolbar theme.</p>
                    <div id="snow-editor" style="height: 300px;">
                        <h3>A powerful and responsive admin dashboard template built on Bootstrap.</h3>
                        <p><br/></p>
                        <ul>
                            <li>
                                Fully responsive layout with a sleek and modern design.
                            </li>
                            <li>
                                Multiple pre-built pages such as login, registration, dashboard, charts, tables, and
                                more.
                            </li>
                            <li>
                                Includes various components like modals, alerts, navigation menus, etc.
                            </li>
                            <li>
                                Easy to customize and extend to suit your project’s needs.
                            </li>
                            <li>
                                Built with Bootstrap 5x, ensuring compatibility with a wide range of devices.
                            </li>
                        </ul>
                        <p><br/></p>
                        <p>
                            Simple Admin is the perfect choice for your next admin project. Get started today and create
                            a stunning interface for your application.
                        </p>
                    </div>
                </div>
                <div class="card-body mt-3">
                    <h5 class="card-title mb-1">Bubble Editor</h5>
                    <p class="text-muted">Bubble is a simple tooltip based theme.</p>
                    <div id="bubble-editor" style="height: 300px;">
                        <h3>A powerful and responsive admin dashboard template built on Bootstrap.</h3>
                        <p><br/></p>
                        <ul>
                            <li>
                                Fully responsive layout with a sleek and modern design.
                            </li>
                            <li>
                                Multiple pre-built pages such as login, registration, dashboard, charts, tables, and
                                more.
                            </li>
                            <li>
                                Includes various components like modals, alerts, navigation menus, etc.
                            </li>
                            <li>
                                Easy to customize and extend to suit your project’s needs.
                            </li>
                            <li>
                                Built with Bootstrap 5x, ensuring compatibility with a wide range of devices.
                            </li>
                        </ul>
                        <p><br/></p>
                        <p>
                            Simple Admin is the perfect choice for your next admin project. Get started today and create
                            a stunning interface for your application.
                        </p>
                    </div> <!-- end Snow-editor-->
                </div>
            </div> <!-- end card-->
        </div> <!-- end col -->
    </div>
</div> <!-- container -->

@section scripts
{
    <script src="/plugins/quill/quill.js"></script>
    <script src="/js/pages/form-quilljs.js"></script>
}
