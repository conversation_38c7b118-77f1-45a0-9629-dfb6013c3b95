
@{
    ViewBag.Title = "Child Rows";
    ViewBag.SubTitle = "Display additional row details with expandable child rows in DataTables for a cleaner and more informative layout.";
    ViewBag.BadgeIcon = "layout-list";
    ViewBag.BadgeTitle = "Expandable Rows";
}

@section styles
{
    <link href="/plugins/datatables/responsive.bootstrap5.min.css" rel="stylesheet" type="text/css"/>
}

<div class="container-fluid">

    @await Html.PartialAsync("~/Views/Shared/Partials/_PageTitle.cshtml")


    <div class="row justify-content-center">
        <div class="col-xxl-12">
            <div class="card">
                <div class="card-header justify-content-between">
                    <h5 class="card-title"> Example </h5>
                    <a class="icon-link icon-link-hover link-primary fw-semibold"
                       href="https://datatables.net/examples/api/row_details" target="_blank">View Docs <i
                            class="ti ti-arrow-right bi align-middle fs-lg"></i></a>
                </div>
                <div class="card-body">
                    <table class="table table-striped dt-responsive align-middle mb-0" id="child-rows-data">
                        <thead class="thead-sm text-uppercase fs-xxs">
                        <tr>
                            <th></th>
                            <th>Company</th>
                            <th>Symbol</th>
                            <th>Price</th>
                            <th>Change</th>
                            <th>Volume</th>
                            <th>Market Cap</th>
                        </tr>
                        </thead>
                    </table>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div>
    </div>
</div>
<!-- container -->

@section scripts
{
    <script src="/plugins/datatables/dataTables.min.js"></script>
    <script src="/plugins/datatables/dataTables.bootstrap5.min.js"></script>
    <script src="/plugins/datatables/dataTables.responsive.min.js"></script>
    <script src="/plugins/datatables/responsive.bootstrap5.min.js"></script>
    <script src="/js/pages/datatables-child-rows.js"></script>
}
