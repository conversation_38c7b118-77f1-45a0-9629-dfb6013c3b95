/**
 * Template Name: Simple - Responsive Admin & Dashboard Template
 * By (Author): Coderthemes
 * Module/App (File Name): Datatables JavaScript Source
 * Version: 3.0.0
 */

const dataSet = [
    ['Company', 'Symbol', 'Price', 'Change', 'Volume', 'Market Cap', 'Rating', 'Status'],
    ['Apple Inc.', 'AAPL', '174.55', '+0.82%', '75214350', '2.80T', 'Buy', 'Bullish'],
    ['Microsoft Corporation', 'MSFT', '315.50', '-0.14%', '23412340', '2.45T', 'Buy', 'Bullish'],
    ['Alphabet Inc.', 'GOOGL', '130.25', '+1.23%', '18452123', '1.72T', 'Hold', 'Bullish'],
    ['Amazon.com Inc.', 'AMZN', '120.10', '+0.94%', '31232145', '1.55T', 'Buy', 'Bullish'],
    ['Tesla Inc.', 'TSLA', '680.75', '-2.10%', '28954320', '800B', 'Sell', 'Bearish'],
    ['NVIDIA Corporation', 'NVDA', '425.60', '+1.80%', '19843210', '1.10T', 'Buy', 'Bullish'],
    ['Meta Platforms Inc.', 'META', '240.45', '-0.75%', '16543250', '700B', 'Hold', 'Neutral'],
    ['Berkshire Hathaway Inc.', 'BRK.A', '520000.00', '+0.35%', '85000', '850B', 'Buy', 'Bullish'],
    ['Johnson & Johnson', 'JNJ', '162.30', '-0.25%', '7243120', '450B', 'Hold', 'Neutral'],
    ['JPMorgan Chase & Co.', 'JPM', '145.80', '+0.60%', '9823140', '450B', 'Buy', 'Bullish'],
    ['Visa Inc.', 'V', '235.50', '+0.25%', '5432123', '500B', 'Buy', 'Bullish'],
    ['UnitedHealth Group', 'UNH', '510.75', '-0.40%', '4321120', '470B', 'Hold', 'Neutral'],
    ['Procter & Gamble Co.', 'PG', '155.30', '+0.10%', '3210000', '370B', 'Hold', 'Neutral'],
    ['Mastercard Incorporated', 'MA', '380.25', '+1.05%', '5123120', '420B', 'Buy', 'Bullish'],
    ['Pfizer Inc.', 'PFE', '39.10', '-0.80%', '********', '220B', 'Hold', 'Neutral'],
    ['Walmart Inc.', 'WMT', '148.90', '+0.30%', '6523120', '400B', 'Buy', 'Bullish'],
    ['Bank of America Corporation', 'BAC', '28.45', '-0.15%', '********', '270B', 'Hold', 'Neutral'],
    ['Home Depot Inc.', 'HD', '325.75', '+0.50%', '4321450', '350B', 'Buy', 'Bullish'],
    ['Chevron Corporation', 'CVX', '158.60', '-0.90%', '7543120', '300B', 'Hold', 'Neutral'],
    ['The Walt Disney Company', 'DIS', '98.75', '+0.65%', '8321450', '180B', 'Hold', 'Neutral'],
    ['Intel Corporation', 'INTC', '32.50', '*****%', '********', '150B', 'Hold', 'Neutral'],
    ['Cisco Systems Inc.', 'CSCO', '48.10', '-0.50%', '********', '200B', 'Hold', 'Neutral'],
    ['Comcast Corporation', 'CMCSA', '42.30', '+0.75%', '9321450', '180B', 'Hold', 'Neutral'],
    ['Coca-Cola Company', 'KO', '62.45', '+0.20%', '8452310', '270B', 'Hold', 'Neutral'],
    ['PepsiCo Inc.', 'PEP', '180.30', '+0.40%', '6321540', '250B', 'Hold', 'Neutral'],
    ['McDonald’s Corporation', 'MCD', '290.75', '+0.50%', '5214300', '210B', 'Buy', 'Bullish'],
    ['AbbVie Inc.', 'ABBV', '152.10', '-0.20%', '4123150', '270B', 'Hold', 'Neutral'],
    ['Adobe Inc.', 'ADBE', '515.60', '*****%', '3214320', '240B', 'Buy', 'Bullish'],
    ['Salesforce Inc.', 'CRM', '220.45', '+0.90%', '4325120', '210B', 'Buy', 'Bullish'],
    ['Netflix Inc.', 'NFLX', '450.30', '*****%', '6321540', '200B', 'Buy', 'Bullish'],
    ['Broadcom Inc.', 'AVGO', '880.75', '*****%', '1987450', '400B', 'Buy', 'Bullish'],
    ['Costco Wholesale Corporation', 'COST', '680.40', '+0.80%', '3214300', '300B', 'Buy', 'Bullish'],
    ['AT&T Inc.', 'T', '16.45', '-0.25%', '41235400', '120B', 'Hold', 'Neutral'],
    ['Verizon Communications Inc.', 'VZ', '36.30', '-0.15%', '28974500', '150B', 'Hold', 'Neutral'],
    ['Qualcomm Incorporated', 'QCOM', '140.50', '+1.30%', '11234500', '180B', 'Buy', 'Bullish'],
    ['Texas Instruments Inc.', 'TXN', '170.60', '+0.55%', '6234500', '160B', 'Hold', 'Neutral'],
    ['Amgen Inc.', 'AMGN', '255.30', '-0.10%', '5213000', '140B', 'Hold', 'Neutral'],
    ['Oracle Corporation', 'ORCL', '105.20', '*****%', '7432500', '300B', 'Buy', 'Bullish'],
    ['Union Pacific Corporation', 'UNP', '225.60', '+0.75%', '4321200', '140B', 'Buy', 'Bullish'],
    ['Honeywell International Inc.', 'HON', '210.30', '+0.20%', '3124500', '150B', 'Hold', 'Neutral'],
    ['Boeing Company', 'BA', '215.40', '-0.60%', '6321000', '130B', 'Hold', 'Neutral'],
    ['Goldman Sachs Group Inc.', 'GS', '360.50', '+0.80%', '1984500', '120B', 'Buy', 'Bullish'],
    ['3M Company', 'MMM', '95.30', '-0.50%', '5234100', '50B', 'Hold', 'Neutral'],
    ['Lockheed Martin Corporation', 'LMT', '465.20', '+0.40%', '1542300', '120B', 'Hold', 'Neutral'],
    ['Caterpillar Inc.', 'CAT', '240.60', '*****%', '4321000', '130B', 'Buy', 'Bullish'],
    ['Starbucks Corporation', 'SBUX', '100.10', '+0.15%', '7231500', '115B', 'Hold', 'Neutral'],
    ['General Electric Company', 'GE', '110.45', '+0.60%', '8213000', '120B', 'Buy', 'Bullish'],
    ['American Express Company', 'AXP', '185.30', '+0.80%', '3124500', '140B', 'Buy', 'Bullish']
];

document.addEventListener('DOMContentLoaded', () => {
    const tableElement = document.getElementById('datatables-javascript-source');
    if (tableElement) {
        new DataTable(tableElement, {
            columns: [
                {title: 'company'},
                {title: 'symbol'},
                {title: 'price'},
                {title: 'change'},
                {title: 'volume'},
                {title: 'market_cap'},
                {title: 'rating'},
                {
                    title: 'status',
                    render: function (data, type, row) {
                        var badgeClass = data === 'Bullish' ? 'badge-soft-success' : 'badge-soft-danger';
                        return `<span class="badge badge-label ${badgeClass}">${data}</span>`;
                    }
                }
            ],
            data: dataSet,
            language: {
                paginate: {
                    first: '<i class="ti ti-chevrons-left"></i>',   // Tabler First
                    previous: '<i class="ti ti-chevron-left"></i>', // Tabler Prev
                    next: '<i class="ti ti-chevron-right"></i>',    // Tabler Next
                    last: '<i class="ti ti-chevrons-right"></i>'    // Tabler Last
                },
                lengthMenu: '_MENU_ Companies per page', // Change text to "Companies"
                info: 'Showing <span class="fw-semibold">_START_ </span> to <span class="fw-semibold">_END_</span> of <span class="fw-semibold">_TOTAL_</span> Companies' // Customize the "Showing" text
            }
        });
    }
})
