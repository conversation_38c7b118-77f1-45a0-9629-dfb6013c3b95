{"name": "simple", "version": "3.0.0", "description": "Simple - Admin & Dashboard Template", "main": "index.js", "scripts": {"dev": "gulp", "build": "gulp build", "rtl": "gulp rtl", "rtl-build": "gulp rtlBuild", "clean": "gulp clean"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "devDependencies": {"@popperjs/core": "^2.11.8", "clean-css": "^5.3.1", "gulp": "^4.0.2", "gulp-autoprefixer": "^8.0.0", "gulp-clean-css": "^4.2.0", "gulp-concat": "^2.6.1", "gulp-rename": "^2.0.0", "gulp-rtlcss": "^2.0.0", "gulp-sass": "^5.1.0", "gulp-sourcemaps": "^3.0.0", "sass": "1.77.6"}, "dependencies": {"@sjmc11/tourguidejs": "^0.0.27", "@tabler/icons-webfont": "^3.31.0", "bootstrap": "^5.3.7", "chart.js": "^4.4.9", "choices.js": "^11.0.3", "clipboard": "^2.0.11", "datatables.net": "2.3.0", "datatables.net-bs5": "2.3.0", "datatables.net-buttons": "^3.2.3", "datatables.net-buttons-bs5": "^3.2.3", "datatables.net-fixedcolumns": "^5.0.4", "datatables.net-fixedcolumns-bs5": "^5.0.4", "datatables.net-fixedheader": "^4.0.1", "datatables.net-fixedheader-bs5": "^4.0.1", "datatables.net-keytable": "2.12.1", "datatables.net-keytable-bs5": "^2.12.1", "datatables.net-responsive": "^3.0.4", "datatables.net-responsive-bs5": "^3.0.4", "datatables.net-select": "^3.0.0", "datatables.net-select-bs5": "^3.0.0", "dropzone": "^6.0.0-beta.2", "flatpickr": "^4.6.13", "fullcalendar": "^6.1.15", "glightbox": "^3.3.0", "handlebars": "^4.7.8", "jquery": "3.7.1", "jsvectormap": "^1.6.0", "jszip": "^3.10.1", "leaflet": "^1.9.4", "lucide": "^0.510.0", "masonry-layout": "^4.2.2", "moment": "^2.30.1", "muuri": "^0.9.5", "pdfmake": "^0.2.20", "prismjs": "^1.29.0", "quill": "^2.0.3", "simplebar": "^6.3.0", "sortablejs": "^1.15.6", "spinkit": "^2.0.1", "sweetalert2": "^11.15.10", "typeahead.js": "^0.11.1", "web-animations-js": "^2.3.2", "wnumb": "^1.2.0"}}