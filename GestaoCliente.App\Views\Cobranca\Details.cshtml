@model GestaoCliente.App.Models.Cobranca

@{
    ViewData["Title"] = "Detalhes da Cobrança";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">@ViewData["Title"]</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Cliente</label>
                            <p class="form-control-plaintext">@Model.Cliente.Nome</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Valor Total</label>
                            <p class="form-control-plaintext">
                                <strong class="text-primary fs-5">@Model.ValorTotal.ToString("C")</strong>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Número de Parcelas</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-info fs-6">@Model.Parcelas parcelas</span>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Data de Vencimento</label>
                            <p class="form-control-plaintext">@Model.DataVencimento.ToString("dd/MM/yyyy")</p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Data de Criação</label>
                            <p class="form-control-plaintext">@Model.DataCriacao.ToString("dd/MM/yyyy HH:mm")</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Status</label>
                            <p class="form-control-plaintext">
                                @{
                                    var pagoCount = Model.Pagamentos.Count(p => p.Pago);
                                    var totalParcelas = Model.Parcelas;
                                    var statusClass = pagoCount == totalParcelas ? "success" : pagoCount > 0 ? "warning" : "danger";
                                    var statusText = pagoCount == totalParcelas ? "Quitado" : pagoCount > 0 ? "Parcial" : "Pendente";
                                }
                                <span class="badge bg-@statusClass fs-6">
                                    @statusText (@pagoCount/@totalParcelas)
                                </span>
                            </p>
                        </div>
                    </div>
                </div>

                @if (Model.Pagamentos.Any())
                {
                    <div class="row">
                        <div class="col-12">
                            <h5 class="mb-3">
                                <i data-lucide="credit-card"></i> Pagamentos
                            </h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Parcela</th>
                                            <th>Valor</th>
                                            <th>Vencimento</th>
                                            <th>Status</th>
                                            <th>Data Pagamento</th>
                                            <th>Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @{
                                            var parcelaNumero = 1;
                                        }
                                        @foreach (var pagamento in Model.Pagamentos.OrderBy(p => p.DataVencimento))
                                        {
                                            var isVencido = !pagamento.Pago && pagamento.DataVencimento < DateTime.Now;
                                            var rowClass = pagamento.Pago ? "table-success" : isVencido ? "table-danger" : "";
                                            <tr class="@rowClass">
                                                <td>
                                                    <strong>@parcelaNumero/@Model.Parcelas</strong>
                                                </td>
                                                <td>
                                                    <strong>@pagamento.Valor.ToString("C")</strong>
                                                </td>
                                                <td>
                                                    @pagamento.DataVencimento.ToString("dd/MM/yyyy")
                                                    @if (isVencido)
                                                    {
                                                        <br><small class="text-danger">
                                                            @((DateTime.Now - pagamento.DataVencimento).Days) dias em atraso
                                                        </small>
                                                    }
                                                </td>
                                                <td>
                                                    @if (pagamento.Pago)
                                                    {
                                                        <span class="badge bg-success">
                                                            <i data-lucide="check"></i> Pago
                                                        </span>
                                                    }
                                                    else if (isVencido)
                                                    {
                                                        <span class="badge bg-danger">
                                                            <i data-lucide="alert-triangle"></i> Vencido
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-warning">
                                                            <i data-lucide="clock"></i> Pendente
                                                        </span>
                                                    }
                                                </td>
                                                <td>
                                                    @if (pagamento.DataPagamento.HasValue)
                                                    {
                                                        @pagamento.DataPagamento.Value.ToString("dd/MM/yyyy")
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        @if (!pagamento.Pago)
                                                        {
                                                            <form asp-controller="Pagamento" asp-action="MarcarComoPago" asp-route-id="@pagamento.Id" method="post" style="display: inline;">
                                                                <button type="submit" class="btn btn-sm btn-success" title="Marcar como Pago"
                                                                        onclick="return confirm('Marcar este pagamento como pago?')">
                                                                    <i data-lucide="check"></i>
                                                                </button>
                                                            </form>
                                                        }
                                                        <a asp-controller="Pagamento" asp-action="Edit" asp-route-id="@pagamento.Id" 
                                                           class="btn btn-sm btn-outline-warning" title="Editar">
                                                            <i data-lucide="edit"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                            parcelaNumero++;
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                }

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i data-lucide="arrow-left"></i> Voltar
                            </a>
                            <div>
                                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                    <i data-lucide="edit"></i> Editar
                                </a>
                                <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                                    <i data-lucide="trash-2"></i> Excluir
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
