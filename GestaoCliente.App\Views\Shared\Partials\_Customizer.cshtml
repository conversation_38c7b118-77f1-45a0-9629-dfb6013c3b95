<!-- Theme Settings -->
<div class="offcanvas offcanvas-end overflow-hidden" tabindex="-1" id="theme-settings-offcanvas">
    <div class="d-flex justify-content-between text-bg-primary gap-2 p-3" style="background-image: url(/images/user-bg-pattern.png);">
        <div>
            <h5 class="mb-1 fw-bold text-white text-uppercase">Admin Customizer</h5>
            <p class="text-white text-opacity-75 fst-italic fw-medium mb-0">Easily configure layout, styles, and preferences for your admin interface.</p>
        </div>

        <div class="flex-grow-0">
            <button type="button" class="d-block btn btn-sm bg-white bg-opacity-25 text-white rounded-circle btn-icon" data-bs-dismiss="offcanvas"><i class="ti ti-x fs-lg"></i></button>
        </div>
    </div>

    <div class="offcanvas-body p-0 h-100" data-simplebar>

        <div class="p-3 border-bottom border-dashed">
            <h5 class="mb-3 fw-bold">Color Scheme</h5>
            <div class="row">
                <div class="col-4">
                    <div class="form-check card-radio">
                        <input class="form-check-input" type="radio" name="data-bs-theme" id="layout-color-light" value="light">
                        <label class="form-check-label p-0 w-100" for="layout-color-light">
                            <img src="/images/layouts/light.svg" alt="layout-img" class="img-fluid">
                        </label>
                    </div>
                    <h5 class="text-center text-muted mt-2 mb-0">Light</h5>
                </div>

                <div class="col-4">
                    <div class="form-check card-radio">
                        <input class="form-check-input" type="radio" name="data-bs-theme" id="layout-color-dark" value="dark">
                        <label class="form-check-label p-0 w-100 overflow-hidden" for="layout-color-dark">
                            <img src="/images/layouts/dark.svg" alt="layout-img" class="img-fluid overflow-hidden">
                        </label>
                    </div>
                    <h5 class="text-center text-muted mt-2 mb-0">Dark</h5>
                </div>
            </div>
        </div>

        <div class="p-3 border-bottom border-dashed">
            <h5 class="mb-3 fw-bold">Topbar Color</h5>

            <div class="row g-3">
                <div class="col-4">
                    <div class="form-check card-radio">
                        <input class="form-check-input" type="radio" name="data-topbar-color" id="topbar-color-light" value="light">
                        <label class="form-check-label p-0 w-100" for="topbar-color-light">
                            <img src="/images/layouts/topbar-light.svg" alt="layout-img" class="img-fluid">
                        </label>
                    </div>
                    <h5 class="text-center text-muted mt-2 mb-0">Light</h5>
                </div>

                <div class="col-4">
                    <div class="form-check card-radio">
                        <input class="form-check-input" type="radio" name="data-topbar-color" id="topbar-color-dark" value="dark">
                        <label class="form-check-label p-0 w-100" for="topbar-color-dark">
                            <img src="/images/layouts/topbar-dark.svg" alt="layout-img" class="img-fluid">
                        </label>
                    </div>
                    <h5 class="fs-sm text-center text-muted mt-2 mb-0">Dark</h5>
                </div>
            </div>
        </div>

        <div class="p-3 border-bottom border-dashed">
            <h5 class="mb-3 fw-bold">Sidenav Color</h5>

            <div class="row g-3">
                <div class="col-4">
                    <div class="form-check sidebar-setting card-radio">
                        <input class="form-check-input" type="radio" name="data-sidenav-color" id="sidenav-color-light" value="light">
                        <label class="form-check-label p-0 w-100" for="sidenav-color-light">
                            <img src="/images/layouts/light.svg" alt="layout-img" class="img-fluid">
                        </label>
                    </div>
                    <h5 class="fs-sm text-center text-muted mt-2 mb-0">Light</h5>
                </div>

                <div class="col-4">
                    <div class="form-check sidebar-setting card-radio">
                        <input class="form-check-input" type="radio" name="data-sidenav-color" id="sidenav-color-dark" value="dark">
                        <label class="form-check-label p-0 w-100" for="sidenav-color-dark">
                            <img src="/images/layouts/sidenav-dark.svg" alt="layout-img" class="img-fluid">
                        </label>
                    </div>
                    <h5 class="fs-sm text-center text-muted mt-2 mb-0">Dark</h5>
                </div>
            </div>
        </div>

        <div class="p-3 border-bottom border-dashed">
            <h5 class="mb-3 fw-bold">Sidebar Size</h5>

            <div class="row g-3">
                <div class="col-4">
                    <div class="form-check sidebar-setting card-radio">
                        <input class="form-check-input" type="radio" name="data-sidenav-size" id="sidenav-size-small-hover-active" value="default">
                        <label class="form-check-label p-0 w-100" for="sidenav-size-small-hover-active">
                            <img src="/images/layouts/light.svg" alt="layout-img" class="img-fluid">
                        </label>
                    </div>
                    <h5 class="mb-0 fs-base text-center text-muted mt-2">Default</h5>
                </div>

                <div class="col-4">
                    <div class="form-check sidebar-setting card-radio">
                        <input class="form-check-input" type="radio" name="data-sidenav-size" id="sidenav-size-small-hover" value="collapse">
                        <label class="form-check-label p-0 w-100" for="sidenav-size-small-hover">
                            <img src="/images/layouts/sidebar-condensed.svg" alt="layout-img" class="img-fluid">
                        </label>
                    </div>
                    <h5 class="mb-0 text-center text-muted mt-2">Collapse</h5>
                </div>
            </div>
        </div>

        <div class="p-3 border-bottom border-dashed">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="fw-bold mb-0">Layout Position</h5>

                <div class="btn-group radio" role="group">
                    <input type="radio" class="btn-check" name="data-layout-position" id="layout-position-fixed" value="fixed">
                    <label class="btn btn-sm btn-soft-primary w-sm" for="layout-position-fixed">Fixed</label>

                    <input type="radio" class="btn-check" name="data-layout-position" id="layout-position-scrollable" value="scrollable">
                    <label class="btn btn-sm btn-soft-primary w-sm ms-0" for="layout-position-scrollable">Scrollable</label>
                </div>
            </div>
        </div>

        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><label class="fw-bold m-0" for="sidebaruser-check">Sidebar User Info</label></h5>

                <div class="form-check form-switch fs-lg">
                    <input type="checkbox" class="form-check-input" name="sidebar-user" id="sidebaruser-check">
                </div>
            </div>
        </div>
    </div>

    <div class="offcanvas-footer border-top p-3 text-center">
        <div class="row">
            <div class="col-6">
                <button type="button" class="btn btn-light fw-semibold py-2 w-100" id="reset-layout">Reset</button>
            </div>
            <div class="col-6">
                <a href="#" target="_blank" class="btn btn-dark py-2 fw-semibold w-100">Buy Now</a>
            </div>
        </div>
    </div>
</div>