using Microsoft.AspNetCore.Mvc;

namespace Simple.Controllers
    {
        public class TablesController : Controller
        {
            public IActionResult DatatablesCheckboxSelect()
        {
            return View();
        }

        public IActionResult DatatablesBasic()
        {
            return View();
        }

        public IActionResult Static()
        {
            return View();
        }

        public IActionResult DatatablesChildRows()
        {
            return View();
        }

        public IActionResult DatatablesAjax()
        {
            return View();
        }

        public IActionResult DatatablesRendering()
        {
            return View();
        }

        public IActionResult DatatablesJavascript()
        {
            return View();
        }

        public IActionResult DatatablesExportData()
        {
            return View();
        }

        public IActionResult DatatablesColumns()
        {
            return View();
        }

        public IActionResult DatatablesSelect()
        {
            return View();
        }
        }
    }