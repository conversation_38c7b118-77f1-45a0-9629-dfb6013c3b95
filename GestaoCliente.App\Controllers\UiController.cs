using Microsoft.AspNetCore.Mvc;

namespace Simple.Controllers
    {
        public class UiController : Controller
        {
            public IActionResult MenuLinks()
        {
            return View();
        }

        public IActionResult Links()
        {
            return View();
        }

        public IActionResult Core()
        {
            return View();
        }

        public IActionResult VisualFeedback()
        {
            return View();
        }

        public IActionResult Utilities()
        {
            return View();
        }

        public IActionResult Interactive()
        {
            return View();
        }

        public IActionResult DesignEssentials()
        {
            return View();
        }
        }
    }