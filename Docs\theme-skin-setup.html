<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Dashboard | Simple - Responsive Bootstrap 5 Admin Dashboard</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Simple is the #1 best-selling admin dashboard template on WrapBootstrap. Perfect for building CRM, CMS, project management tools, and custom web apps with clean UI, responsive design, and powerful features.">
    <meta name="keywords" content="Simple, admin dashboard, WrapBootstrap, HTML template, Bootstrap admin, CRM template, CMS template, responsive admin, web app UI, admin theme, best admin template">
    <meta name="author" content="Coderthemes">

    <!-- App favicon -->
    <link rel="shortcut icon" href="assets/images/favicon.ico">

    <!-- Theme Config Js -->
    <script src="assets/js/config.js"></script>

    <!-- Vendor css -->
    <link href="assets/css/vendor.min.css" rel="stylesheet" type="text/css">

    <!-- App css -->
    <link href="assets/css/app.min.css" rel="stylesheet" type="text/css" id="app-style">

    <!-- Icons css -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/tabler-icons.min.css" rel="stylesheet" type="text/css">
</head>

<body>
    <!-- Begin page -->
    <div class="wrapper">


        <!-- Sidenav Menu Start -->
        <div class="sidenav-menu">

            

            <div class="scrollbar" data-simplebar>

                <!--- Sidenav Menu -->
                <ul class="side-nav">
                    <li class="side-nav-title">Menu</li>

                    <!-- Introduction -->
                    <li class="side-nav-item">
                        <a href="index.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-shield-check"></i></span>
                            <span class="menu-text">Introduction</span>
                        </a>
                    </li>

                    <!-- Folder Structure -->
                    <li class="side-nav-item">
                        <a href="folder-structure.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-folders"></i></span>
                            <span class="menu-text">Folder Structure</span>
                        </a>
                    </li>

                    <!-- Getting Started -->
                    <li class="side-nav-item">
                        <a href="getting-started.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-rocket"></i></span>
                            <span class="menu-text">Getting Started</span>
                        </a>
                    </li>




                    <!-- Theme Setup -->
                    <li class="side-nav-item">
                        <a href="theme-skin-setup.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-diamond"></i></span>
                            <span class="menu-text">Theme Skin Setup</span>
                        </a>
                    </li>

                    <!-- Dark Mode -->
                    <li class="side-nav-item">
                        <a href="dark-mode.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-moon"></i></span>
                            <span class="menu-text">Dark Mode</span>
                        </a>
                    </li>

                    <!-- RTL Version -->
                    <li class="side-nav-item">
                        <a href="rtl-version.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-layout-sidebar-right"></i></span>
                            <span class="menu-text">RTL Version</span>
                        </a>
                    </li>

                    <!-- Sources & Credits -->
                    <li class="side-nav-item">
                        <a href="sources.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-alert-circle"></i></span>
                            <span class="menu-text">Sources & Credits</span>
                        </a>
                    </li>

                    <!-- Changelog -->
                    <li class="side-nav-item">
                        <a href="changelog.html" class="side-nav-link">
                            <span class="menu-icon"><i class="ti ti-book"></i></span>
                            <span class="menu-text">Changelog</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <!-- Sidenav Menu End -->

        <!-- Topbar Start -->
        <header class="app-topbar">
            <div class="container-fluid topbar-menu d-flex align-items-center">
                <div class="d-flex align-items-center gap-2">
                    <!-- Topbar Brand Logo -->
                    <div class="logo-topbar">
                        <!-- Logo light -->
                        <a href="index.html" class="logo-light">
                            <span class="logo-lg">
                                <img src="assets/images/logo.png" alt="logo">
                            </span>
                            <span class="logo-sm">
                                <img src="assets/images/logo-sm.png" alt="small logo">
                            </span>
                        </a>

                        <!-- Logo Dark -->
                        <a href="index.html" class="logo-dark">
                            <span class="logo-lg">
                                <img src="assets/images/logo-black.png" alt="dark logo">
                            </span>
                            <span class="logo-sm">
                                <img src="assets/images/logo-sm.png" alt="small logo">
                            </span>
                        </a>
                    </div>




                </div> <!-- .d-flex-->

            </div>
        </header>
        <!-- Topbar End -->



        <!-- ============================================================== -->
        <!-- Start Page Content here -->
        <!-- ============================================================== -->

        <div class="content-page">

            <div class="container mt-3">
                <div class="card">
                    <div class="card-header">
                        <h4 class="font-weight-semibold mb-0">Using Pre-built Themes from Simple.0</h4>
                    </div>

                    <div class="card-body">
                        <p class="text-muted mb-4">
                            Switch between multiple built-in themes by setting the <code>data-skin</code> attribute in the <code>&lt;html&gt;</code> tag. Themes like Classic, Material, Modern, SaaS, Flat, and Minimal are pre-configured for quick integration and visual consistency across your layout.
                        </p>

                        <h5 class="mb-2">Default Skin</h5>
                        <div class="alert alert-primary alert-bordered border-start border-primary d-flex align-items-center gap-2 mb-3">
                            <i class="ti ti-info-circle fs-20"></i>
                            <div class="fw-bold text-decoration-underline">
                                Simple comes with theme set as the default.
                            </div>
                        </div>

                        <div class="alert alert-secondary alert-bordered border-start border-secondary d-flex align-items-center gap-2 mb-3">
                            <i class="ti ti-info-circle fs-20"></i>
                            <div>
                                Set <code>data-skin="two"</code> in the <code>&lt;html&gt;</code>.
                            </div>
                        </div>

                        <div class="alert alert-success alert-bordered border-start border-success d-flex align-items-center gap-2 mb-3">
                            <i class="ti ti-info-circle fs-20"></i>
                            <div>
                                Set <code>data-skin="three"</code> in the <code>&lt;html&gt;</code>.
                            </div>
                        </div>

                        <div class="alert alert-info alert-bordered border-start border-info d-flex align-items-center gap-2 mb-3">
                            <i class="ti ti-info-circle fs-20"></i>
                            <div>
                                Set <code>data-skin="four"</code> in the <code>&lt;html&gt;</code>.
                            </div>
                        </div>

                        <div class="alert alert-purple alert-bordered border-start border-purple d-flex align-items-center gap-2 mb-3">
                            <i class="ti ti-info-circle fs-20"></i>
                            <div>
                                Set <code>data-skin="five"</code> in the <code>&lt;html&gt;</code>.
                            </div>
                        </div>

                        <div class="alert alert-danger alert-bordered border-start border-danger d-flex align-items-center gap-2 mb-0">
                            <i class="ti ti-info-circle fs-20"></i>
                            <div>
                                Set <code>data-skin="six"</code> in the <code>&lt;html&gt;</code>.
                            </div>
                        </div>
                    </div>
                </div>

            </div> <!-- container -->

            <!-- Footer Start -->
            <footer class="footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-6 text-center text-md-start">
                            © 2015 -
                            <script>document.write(new Date().getFullYear())</script> Simple By <span class="fw-semibold">Coderthemes</span>
                        </div>
                        <div class="col-md-6">
                            <div class="text-md-end d-none d-md-block">
                                10GB of <span class="fw-bold">250GB</span> Free.
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <!-- end Footer -->

        </div>

        <!-- ============================================================== -->
        <!-- End Page content -->
        <!-- ============================================================== -->

    </div>
    <!-- END wrapper -->


    <!-- Vendor js -->
    <script src="assets/js/vendor.min.js"></script>

    <!-- App js -->
    <script src="assets/js/app.js"></script>

</body>

</html>