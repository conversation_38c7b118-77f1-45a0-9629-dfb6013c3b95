//
//  _touchpin.scss
//

[data-touchspin] {
    overflow: hidden;
    height: calc($input-padding-y * 2 + $input-btn-font-size * 1.5 + $border-width * 2);
    border: 1px solid var(--#{$prefix}border-color);
    border-radius: var(--#{$prefix}border-radius);

    .form-control {
        text-align: center;

        &:focus {
            box-shadow: none;
            outline: none;
            z-index: 0;
        }
    }

    .btn-group-vertical {
        .btn {
            border-radius: 0;
            height: calc(calc($input-padding-y * 2 + $input-btn-font-size * 1.5 + $border-width * 2) * 0.5);
            padding: 2px 6px;
        }
    }

    .btn {
        border-radius: var(--#{$prefix}border-radius);
        padding: 6px;
    }

    .floating {
        display: flex;
        align-items: center;
        padding: 4px 6px;
        margin: 4px !important;
        border-radius: var(--#{$prefix}border-radius) !important;

        &.rounded-circle {
            border-radius: 50% !important;
        }
    }

    &.input-group-sm {
        height: calc($input-padding-y-sm * 2 + $input-font-size-sm * 1.5 + $border-width * 2);

        .btn {
            padding: 4px;
        }

        .floating {
            padding: 2px 3px;
            margin: 4px;
            border-radius: var(--#{$prefix}border-radius-sm) !important;
        }

        .btn-group-vertical {
            .btn {
                height: calc(calc($input-padding-y-sm * 2 + $input-font-size-sm * 1.5 + $border-width * 2) * 0.5);
            }
        }
    }

    &.input-group-lg {
        height: calc($input-padding-y-lg * 2 + $input-font-size-lg * 1.5 + $border-width * 2);

        .floating {
            padding: 4px 6px;
            margin: 4px;
            border-radius: var(--#{$prefix}border-radius-lg) !important;
        }
        
        .btn-group-vertical {
            .btn {
                height: calc(calc($input-padding-y-lg * 2 + $input-font-size-lg * 1.5 + $border-width * 2) * 0.5);
            }
        }
    }
}
