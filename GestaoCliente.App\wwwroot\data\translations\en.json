{"user-role": "Art Director", "menu-title": "<PERSON><PERSON>", "dashboard": "Dashboard", "analytics": "Analytics", "landing-page": "<PERSON>", "apps-title": "Apps", "users": "Users", "contacts": "Contacts", "roles": "Roles", "permissions": "Permissions", "projects": "Projects", "projects-list": "Projects List", "project-details": "View Project", "project-kanban": "Kanban Board", "project-team-board": "Team Board", "project-activity": "Activity Steam", "file-manager": "File Manager", "chat": "Cha<PERSON>", "calendar": "Calendar", "directory": "Directory", "invoice": "Invoice", "invoices": "Invoices", "invoice-details": "Single Invoice", "invoice-create": "New Invoice", "otherapps": "Other Apps", "companies": "Companies", "clients": "Clients", "outlook": "Outlook View", "vote-list": "Vote List", "issue-tracker": "Issue Tracker", "api-keys": "API Keys", "blog": "Blog", "article": "Article", "pin-board": "Pin Board", "forum-view": "Forum View", "forum-post": "Forum Post", "pages": "Pages", "pages-profile": "Profile", "pages-faq": "FAQ", "pages-pricing": "Pricing", "pages-empty": "Empty Page", "pages-timeline": "Timeline", "pages-search-results": "Search Results", "pages-coming-soon": "Coming Soon", "pages-terms-conditions": "Terms & Conditions", "miscellaneous": "Miscellaneous", "misc-nestable": "Nestable List", "misc-text-diff": "Text Diff", "misc-pdf-viewer": "PDF Viewer", "misc-i18": "i18 Support", "misc-sweet-alerts": "<PERSON> Alerts", "misc-idle-timer": "Idle Timer", "misc-pass-meter": "Password Meter", "misc-live-favicon": "Live Favicon", "misc-clipboard": "Clipboard", "misc-tree-view": "Tree View", "misc-loading-buttons": "Loading Buttons", "misc-gallery": "Gallery", "misc-masonry": "Masonry", "misc-tour": "Tour", "misc-animation": "Animation", "authentication": "Authentication", "auth-sign-in": "Sign In", "auth-sign-up": "Sign Up", "auth-reset-pass": "Reset Password", "auth-new-pass": "New Password", "auth-two-factor": "Two Factor", "auth-lock-screen": "Lock Screen", "auth-success-mail": "Success Mail", "auth-login-pin": "Login with PIN", "auth-delete-account": "Delete Account", "error-pages": "Error <PERSON>s", "error-404": "404 – Not Found", "maintenance": "Maintenance", "sidebars": "Sidebars", "sidebar-light": "Light Menu", "sidebar-dark": "Dark Menu", "sidebar-gray": "<PERSON>", "form-plugins": "Plugins", "form-quilljs": "Quilljs Editors", "topbar": "Topbar", "topbar-dark": "Dark Topbar", "topbar-gray": "<PERSON>", "ui-components": "UI Components", "ui-core": "Core Elements", "ui-interactive": "Interactive Features", "ui-menu-links": "Menu & Links", "ui-visual-feedback": "Visual Feedback", "ui-utilities": "Utilities", "widgets": "Widgets", "metrics": "Metrics", "graphs": "Graphs", "chartjs": "Chart js", "charts": "Charts", "forms": "Forms", "form-elements": "Basic Elements", "form-pickers": "Pickers", "form-select": "Select", "form-validation": "Validation", "form-wizard": "<PERSON>", "form-fileuploads": "File Uploads", "form-text-editors": "Text Editors", "form-range-slider": "Range Slider", "form-layouts": "Layouts", "form-other-plugins": "Other Plugins", "tables": "Tables", "tables-static": "Static Tables", "tables-custom": "Custom Tables", "datatables": "DataTables", "tables-datatables-basic": "Basic", "tables-datatables-export-data": "Export Data", "tables-datatables-select": "Select", "tables-datatables-ajax": "Ajax", "tables-datatables-javascript": "Javascript Source", "tables-datatables-rendering": "Data Rendering", "tables-datatables-scroll": "<PERSON><PERSON>", "tables-datatables-columns": "Show & Hide Column", "tables-datatables-child-rows": "Child Rows", "tables-datatables-column-searching": "Column Searching", "tables-datatables-range-search": "Range Search", "tables-datatables-fixed-header": "Fixed Header", "tables-datatables-add-rows": "Add Rows", "tables-datatables-checkbox-select": "Checkbox Select", "icons": "Icons", "icons-tabler": "Tabler", "icons-lucide": "Lucide", "icons-flags": "Flags", "maps": "Maps", "maps-google": "Google Maps", "maps-vector": "Vector Maps", "maps-leaflet": "Leaflet Maps", "menu-levels": "Menu Levels", "second-level": "Second Level", "third-level": "Third Level", "disabled-menu": "Disabled <PERSON><PERSON>", "pages-title": "Custom Pages", "layouts-title": "Layouts", "components-title": "Components", "items-title": "Menu Items", "special-menu": "Special Menu", "topbar-search": "Search for something...", "demo-text": "This is the English version of the text used in this demo. It serves as the default language content before translation is applied. By setting up i18n support, this text can be easily switched to other languages, ensuring a more inclusive and accessible user experience."}