// 
// Corporate Theme Mode
//

$theme-corporate-colors: (
  "primary":   #1f69d8,   // steel blue – confident and stable
  "secondary": #6c5ce7,   // muted violet – creative yet professional
  "success":   #43a047,   // emerald green – positive and reassuring
  "info":      #00acc1,   // teal cyan – clean and modern
  "warning":   #ffa726,   // soft orange – alert but not harsh
  "danger":    #e53935,   // coral red – clear but refined
  "purple":    #9b59b6,   // soft purple – subtle and stylish
  "dark":      #34495e,   // Dark
  "light":     #f0f4f8,   // light blue-gray – fresh and corporate
);

@if $theme-corporate ==true {

    @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

    html[data-skin="corporate"] {

        --#{$prefix}font-sans-serif:        "Inter", sans-serif;

        --#{$prefix}body-bg:               #f8f7fa;

        --#{$prefix}tertiary-bg:           #f8f7fa;

        --#{$prefix}border-radius:               .3rem;
        --#{$prefix}border-radius-sm:            .25rem;
        --#{$prefix}border-radius-lg:            .4rem;
        --#{$prefix}border-radius-xl:            .5rem;

        --#{$prefix}font-weight-medium:      500;
        --#{$prefix}font-weight-semibold:    600;
        --#{$prefix}font-weight-bold:        700;

        --#{$prefix}font-size-xxs:          11px;
        --#{$prefix}font-size-xs:           12px;
        --#{$prefix}font-size-base:         13px;
        --#{$prefix}font-size-md:           14px;
        --#{$prefix}font-size-lg:           16px;
        --#{$prefix}font-size-xl:           18px;
        --#{$prefix}font-size-xxl:          20px;

        --#{$prefix}chart-primary:                           #1f69d8;
        --#{$prefix}chart-primary-rgb:                       #{to-rgb(#1f69d8)};
        --#{$prefix}chart-secondary:                         #43a047;
        --#{$prefix}chart-secondary-rgb:                     #{to-rgb(#43a047)};
        --#{$prefix}chart-gray:                              #e9eaeb;
        --#{$prefix}chart-gray-rgb:                          #{to-rgb(#e9eaeb)};
        --#{$prefix}chart-dark:                              #00acc1;
        --#{$prefix}chart-dark-rgb:                          #{to-rgb(#00acc1)};

        --#{$prefix}theme-card-box-shadow:    0px 1px 4px 0px rgba(130, 143, 163, 0.15);


        @each $name, $value in $theme-corporate-colors {
            --#{$prefix}#{$name}: #{$value};
        }

        @each $name, $value in $theme-corporate-colors {
            --#{$prefix}#{$name}-rgb: #{to-rgb($value)};
        }

        @each $name, $value in $theme-corporate-colors {
            --#{$prefix}#{$name}-bg-subtle: #{rgba($value, 0.15)};
        }

        @each $name, $value in $theme-corporate-colors {
            --#{$prefix}#{$name}-border-subtle: #{rgba($value, 0.3)};
        }

        @each $name, $value in $theme-corporate-colors {
            --#{$prefix}#{$name}-text-emphasis: #{shade-color($value, 25%)};
        }

        // Background Light left-sidebar
        &[data-sidenav-color="light"] {
            --#{$prefix}sidenav-bg:                       #ffffff;
            --#{$prefix}sidenav-border-color:             #{$gray-200};
            --#{$prefix}sidenav-item-color:               #63666a;
            --#{$prefix}sidenav-item-hover-color:         #23303c;
            --#{$prefix}sidenav-item-hover-bg:          #f8f7fa;
            --#{$prefix}sidenav-item-active-color:        #23303c;
            --#{$prefix}sidenav-item-active-bg:         #f8f7fa;
        }

        // Dark Left Sidebar
        &[data-sidenav-color="dark"] {
            --#{$prefix}sidenav-bg: #1c1d28;
            --#{$prefix}sidenav-border-color: #1c1d28;
            --#{$prefix}sidenav-item-color: #6c7889;
            --#{$prefix}sidenav-item-hover-color: #bccee4;
            --#{$prefix}sidenav-item-hover-bg: #20222e;
            --#{$prefix}sidenav-item-active-color: #ced6df;
            --#{$prefix}sidenav-item-active-bg: #20222e;
        }

        // Dark Mode
        &[data-bs-theme="dark"][data-sidenav-color="dark"],
        &[data-bs-theme="dark"][data-sidenav-color="light"] {
           --#{$prefix}sidenav-bg: #1c1d28;
            --#{$prefix}sidenav-border-color: #1c1d28;
            --#{$prefix}sidenav-item-color: #6c7889;
            --#{$prefix}sidenav-item-hover-color: #bccee4;
            --#{$prefix}sidenav-item-hover-bg: #20222e;
            --#{$prefix}sidenav-item-active-color: #ced6df;
            --#{$prefix}sidenav-item-active-bg: #20222e;
        }

        // Light Topbar
        &[data-topbar-color="light"] {
            --#{$prefix}topbar-bg:                      #ffffff;
            --#{$prefix}topbar-item-color:                #63666a;
            --#{$prefix}topbar-item-hover-color:          #{$primary};
            --#{$prefix}topbar-search-bg:                 transparent;
            --#{$prefix}topbar-search-border:           #e7e9eb;
        }

        // Dark Topbar
        &[data-topbar-color="dark"] {
            --#{$prefix}topbar-bg: #252630;
            --#{$prefix}topbar-item-color: #adb5bf;
            --#{$prefix}topbar-item-hover-color: #e0eeff;
            --#{$prefix}topbar-search-bg: #2d2e3c;
            --#{$prefix}topbar-search-border: #2d2e3c;
        }

        // Topbar (Dark Mode)
        &[data-bs-theme="dark"][data-topbar-color="light"],
        &[data-bs-theme="dark"][data-topbar-color="dark"]{
            --#{$prefix}topbar-bg:                      #252630;
            --#{$prefix}topbar-item-color:              #adb5bf;
            --#{$prefix}topbar-item-hover-color:        #e0eeff;
            --#{$prefix}topbar-search-bg:               #2d2e3c;
            --#{$prefix}topbar-search-border:           #2d2e3c;
        }
    }

    @if $enable-dark-mode {
        @include color-mode(dark, true) {
            &[data-skin="corporate"] {
                --#{$prefix}body-bg:                     #{#17181e};
                --#{$prefix}body-bg-rgb:                 #{to-rgb(#17181e)};

                --#{$prefix}border-color:                #252630;

                --#{$prefix}tertiary-bg:           #272832;

                --#{$prefix}secondary-bg:                #{#1b1c22};
                --#{$prefix}secondary-bg-rgb:            #{to-rgb(#1b1c22)};

                --#{$prefix}box-shadow:                  #{0px 0px 30px rgba(0, 0, 0, 0.3)};
                --#{$prefix}theme-card-box-shadow:    none;

                --#{$prefix}light:                     #35363d;
                --#{$prefix}light-rgb:                   #{to-rgb(#35363d)};
                --#{$prefix}light-bg-subtle: rgba(#{to-rgb(#35363d)}, 0.2);
                --#{$prefix}dark:                      #4b4d5c;
                --#{$prefix}dark-rgb:                    #{to-rgb(#4b4d5c)};
            }
        }
    }
}