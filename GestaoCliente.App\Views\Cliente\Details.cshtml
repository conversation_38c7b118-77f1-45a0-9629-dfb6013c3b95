@model GestaoCliente.App.Models.Cliente

@{
    ViewData["Title"] = "Detalhes do Cliente";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="card-title mb-0">@ViewData["Title"]</h4>
                <div>
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning me-2">
                        <i data-lucide="edit"></i> Editar
                    </a>
                    <a asp-controller="Cobranca" asp-action="Create" asp-route-clienteId="@Model.Id" class="btn btn-success">
                        <i data-lucide="plus"></i> Nova Cobrança
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Informações Pessoais</h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Nome:</strong></td>
                                <td>@Html.DisplayFor(model => model.Nome)</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>
                                    @if (!string.IsNullOrEmpty(Model.Email))
                                    {
                                        <a href="mailto:@Model.Email">@Model.Email</a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Não informado</span>
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Telefone:</strong></td>
                                <td><a href="tel:@Model.Telefone">@Html.DisplayFor(model => model.Telefone)</a></td>
                            </tr>
                            <tr>
                                <td><strong>Empresa:</strong></td>
                                <td>
                                    @if (!string.IsNullOrEmpty(Model.NomeEmpresa))
                                    {
                                        @Html.DisplayFor(model => model.NomeEmpresa)
                                    }
                                    else
                                    {
                                        <span class="text-muted">Não informado</span>
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Data de Cadastro:</strong></td>
                                <td>@Model.DataCriacao.ToString("dd/MM/yyyy HH:mm")</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5>Resumo Financeiro</h5>
                        @{
                            var totalCobrancas = Model.Cobrancas.Sum(c => c.ValorTotal);
                            var totalPago = Model.Cobrancas.SelectMany(c => c.Pagamentos).Where(p => p.Pago).Sum(p => p.Valor);
                            var totalPendente = Model.Cobrancas.SelectMany(c => c.Pagamentos).Where(p => !p.Pago).Sum(p => p.Valor);
                        }
                        <div class="row">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">Total em Cobranças</h6>
                                        <h4 class="text-primary">@totalCobrancas.ToString("C")</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-6">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">Pago</h6>
                                        <h5>@totalPago.ToString("C")</h5>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">Pendente</h6>
                                        <h5>@totalPendente.ToString("C")</h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Cobranças -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Cobranças (@Model.Cobrancas.Count)</h5>
                <a asp-controller="Cobranca" asp-action="Create" asp-route-clienteId="@Model.Id" class="btn btn-sm btn-success">
                    <i class="fas fa-plus"></i> Nova Cobrança
                </a>
            </div>
            <div class="card-body">
                @if (Model.Cobrancas.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>Valor Total</th>
                                    <th>Parcelas</th>
                                    <th>Vencimento</th>
                                    <th>Status</th>
                                    <th>Data Criação</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var cobranca in Model.Cobrancas.OrderByDescending(c => c.DataCriacao))
                                {
                                    var pagoCount = cobranca.Pagamentos.Count(p => p.Pago);
                                    var totalParcelas = cobranca.Parcelas;
                                    var statusClass = pagoCount == totalParcelas ? "success" : pagoCount > 0 ? "warning" : "danger";
                                    var statusText = pagoCount == totalParcelas ? "Quitado" : pagoCount > 0 ? "Parcial" : "Pendente";
                                    <tr>
                                        <td><strong>@cobranca.ValorTotal.ToString("C")</strong></td>
                                        <td>@cobranca.Parcelas</td>
                                        <td>@cobranca.DataVencimento.ToString("dd/MM/yyyy")</td>
                                        <td>
                                            <span class="badge bg-@statusClass">
                                                @statusText (@pagoCount/@totalParcelas)
                                            </span>
                                        </td>
                                        <td>@cobranca.DataCriacao.ToString("dd/MM/yyyy")</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-controller="Cobranca" asp-action="Details" asp-route-id="@cobranca.Id"
                                                   class="btn btn-sm btn-outline-info" title="Detalhes">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-controller="Cobranca" asp-action="Edit" asp-route-id="@cobranca.Id"
                                                   class="btn btn-sm btn-outline-warning" title="Editar">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="fas fa-file-invoice-dollar fa-2x text-muted mb-3"></i>
                        <h6 class="text-muted">Nenhuma cobrança cadastrada</h6>
                        <p class="text-muted">Clique no botão "Nova Cobrança" para começar.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<div class="row mt-3">
    <div class="col-12">
        <div class="d-flex justify-content-between">
            <a asp-action="Index" class="btn btn-secondary">
                <i data-lucide="arrow-left"></i> Voltar para Lista
            </a>
            <div>
                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning me-2">
                    <i data-lucide="edit"></i> Editar Cliente
                </a>
                <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                    <i data-lucide="trash-2"></i> Excluir Cliente
                </a>
            </div>
        </div>
    </div>
</div>
