//
// _forms.scss
//

// Form Control
.form-control,
.form-select,
.form-check-input {
    transition: box-shadow 0.2s ease;
    box-shadow: $input-box-shadow;
}

// Form Control Small
.form-control-sm {
    line-height: normal;
}


// Form elements (Color and Range)
input.form-control[type="color"],
input.form-control[type="range"] {
    min-height: $input-height;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

@each $state in map-keys($theme-colors) {
    .form-check-#{$state} {
        .form-check-input {
            &:checked {
                background-color: var(--#{$prefix}#{$state});
                border-color: var(--#{$prefix}#{$state});
            }
        }
    }
}


// card radio
.card-radio {
    padding: 0;

    .form-check-label {
        background-color: $card-bg;
        border: 1px solid var(--#{$prefix}border-color);
        border-radius: $border-radius;
        padding: 0.5rem;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: block;
        position: relative;

        &:hover {
            cursor: pointer;
        }
    }

    .form-check-input {
        display: none;

        &:checked+.form-check-label {
            &:before {
                content: "\f6df";
                font-family: "tabler-icons";
                position: absolute;
                bottom: -10px;
                height: 30px;
                right: -10px;
                width: 30px;
                border-radius: 50%;
                text-align: center;
                font-size: 24px;
                line-height: 30px;
                color: var(--#{$prefix}danger);
                background-color: $card-bg;
            }
        }
    }
}

// Custom Form Check (Gray Styled)
.form-check-input-light {
    background-color: rgba(var(--#{$prefix}light-rgb), 0.9);
    border-color: rgba(var(--#{$prefix}light-rgb), 0.9);
    box-shadow: inset 0 1px 2px rgba(var(--#{$prefix}dark-rgb), 0.05);
}