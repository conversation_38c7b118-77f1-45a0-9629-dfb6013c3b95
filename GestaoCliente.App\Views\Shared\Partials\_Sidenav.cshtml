<!-- Sidenav Menu Start -->
<div class="sidenav-menu">
    <div class="scrollbar" data-simplebar>

        <!-- User -->
        <div class="sidenav-user text-nowrap border border-dashed rounded-3">
            <a href="#!" class="sidenav-user-name d-flex align-items-center">
                <img src="/images/users/user-2.jpg" width="36" class="rounded-circle me-2 d-flex"
                     alt="user-image">
                <span>
                    <h5 class="my-0 fw-semibold"><PERSON><PERSON></h5>
                    <h6 class="my-0 text-muted">Admin Head</h6>
                </span>
            </a>
        </div>

        <!--- Sidenav Menu -->
        <ul class="side-nav">

            <li class="side-nav-item">
                <a href=@Url.Action("Index", "Dashboard") class="side-nav-link">
                    <span class="menu-icon"><i data-lucide="circle-gauge"></i></span>
                    <span class="menu-text" data-lang="dashboard">Dashboard</span>
                </a>
            </li>

            <li class="side-nav-title mt-2">Gestão de Clientes</li>

            <li class="side-nav-item">
                <a data-bs-toggle="collapse" href="#sidebarGestaoClientes" aria-expanded="false" aria-controls="sidebarGestaoClientes"
                   class="side-nav-link">
                    <span class="menu-icon"><i data-lucide="users"></i></span>
                    <span class="menu-text">Gestão</span>
                    <span class="menu-arrow"></span>
                </a>
                <div class="collapse" id="sidebarGestaoClientes">
                    <ul class="sub-menu">
                        <li class="side-nav-item">
                            <a href=@Url.Action("Index", "Cliente") class="side-nav-link">
                                <span class="menu-text">Clientes</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("Index", "Cobranca") class="side-nav-link">
                                <span class="menu-text">Cobranças</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("Index", "Pagamento") class="side-nav-link">
                                <span class="menu-text">Pagamentos</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </li>

            <li class="side-nav-item">
                <a href=@Url.Action("TonAi", "Ai") class="side-nav-link">
                    <span class="menu-icon"><i data-lucide="sparkles"></i></span>
                    <span class="menu-text"> Ton AI </span>
                    <span class="badge text-bg-primary">Hot</span>
                </a>
            </li>

            <li class="side-nav-item">
                <a href=@Url.Action("Calendar", "Apps") class="side-nav-link">
                    <span class="menu-icon"><i data-lucide="calendar"></i></span>
                    <span class="menu-text" data-lang="calendar"> Calendar </span>
                </a>
            </li>

            <li class="side-nav-item">
                <a href=@Url.Action("Directory", "Apps") class="side-nav-link">
                    <span class="menu-icon"><i data-lucide="book-user"></i></span>
                    <span class="menu-text" data-lang="directory"> Directory </span>
                </a>
            </li>

            <li class="side-nav-title mt-2" data-lang="pages-title">Custom Pages</li>

            <li class="side-nav-item">
                <a data-bs-toggle="collapse" href="#sidebarPages" aria-expanded="false" aria-controls="sidebarPages"
                   class="side-nav-link">
                    <span class="menu-icon"><i data-lucide="notebook-text"></i></span>
                    <span class="menu-text" data-lang="pages"> Pages </span>
                    <span class="menu-arrow"></span>
                </a>
                <div class="collapse" id="sidebarPages">
                    <ul class="sub-menu">
                        <li class="side-nav-item">
                            <a href=@Url.Action("Pricing", "Pages") class="side-nav-link">
                                <span class="menu-text" data-lang="pages-pricing">Pricing</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("EmptyPage", "Pages") class="side-nav-link">
                                <span class="menu-text" data-lang="pages-empty">Empty Page</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("Timeline", "Pages") class="side-nav-link">
                                <span class="menu-text" data-lang="pages-timeline">Timeline</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("TermsConditions", "Pages") class="side-nav-link">
                                <span class="menu-text" data-lang="pages-terms-conditions">Terms & Conditions</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("Invoice", "Pages") class="side-nav-link">
                                <span class="menu-text" data-lang="invoice">Invoice</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </li>

            <li class="side-nav-item">
                <a data-bs-toggle="collapse" href="#sidebarPagesAuth" aria-expanded="false"
                   aria-controls="sidebarPagesAuth" class="side-nav-link">
                    <span class="menu-icon"><i data-lucide="fingerprint"></i></span>
                    <span class="menu-text" data-lang="authentication"> Authentication </span>
                    <span class="menu-arrow"></span>
                </a>
                <div class="collapse" id="sidebarPagesAuth">
                    <ul class="sub-menu">
                        <li class="side-nav-item">
                            <a href=@Url.Action("SignIn", "Auth") class="side-nav-link">
                                <span class="menu-text" data-lang="auth-sign-in">Sign In</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("SignUp", "Auth") class="side-nav-link">
                                <span class="menu-text" data-lang="auth-sign-up">Sign Up</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("ResetPass", "Auth") class="side-nav-link">
                                <span class="menu-text" data-lang="auth-reset-pass">Reset Password</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("NewPass", "Auth") class="side-nav-link">
                                <span class="menu-text" data-lang="auth-new-pass">New Password</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("TwoFactor", "Auth") class="side-nav-link">
                                <span class="menu-text" data-lang="auth-two-factor">Two Factor</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("LockScreen", "Auth") class="side-nav-link">
                                <span class="menu-text" data-lang="auth-lock-screen">Lock Screen</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("Error404", "Error") class="side-nav-link">
                                <span class="menu-text" data-lang="error-404">404 – Not Found</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </li>

            <li class="side-nav-item">
                <a data-bs-toggle="collapse" href="#sidebarBaseUI" aria-expanded="false" aria-controls="sidebarBaseUI"
                   class="side-nav-link">
                    <span class="menu-icon"><i data-lucide="pencil-ruler"></i></span>
                    <span class="menu-text" data-lang="ui-components"> UI Components </span>
                    <span class="menu-arrow"></span>
                </a>
                <div class="collapse" id="sidebarBaseUI">
                    <ul class="sub-menu">
                        <li class="side-nav-item">
                            <a href=@Url.Action("Core", "Ui") class="side-nav-link">
                                <span class="menu-text" data-lang="ui-core">Core Elements</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("Interactive", "Ui") class="side-nav-link">
                                <span class="menu-text" data-lang="ui-interactive">Interactive Features</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("MenuLinks", "Ui") class="side-nav-link">
                                <span class="menu-text" data-lang="ui-menu-links">Menu & Links</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("VisualFeedback", "Ui") class="side-nav-link">
                                <span class="menu-text" data-lang="ui-visual-feedback">Visual Feedback</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("Utilities", "Ui") class="side-nav-link">
                                <span class="menu-text" data-lang="ui-utilities">Utilities</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </li>

            <li class="side-nav-item">
                <a href=@Url.Action("Index", "Charts") class="side-nav-link">
                    <span class="menu-icon"><i data-lucide="chart-pie"></i></span>
                    <span class="menu-text" data-lang="charts"> Charts </span>
                </a>
            </li>

            <li class="side-nav-item">
                <a data-bs-toggle="collapse" href="#sidebarForms" aria-expanded="false" aria-controls="sidebarForms"
                   class="side-nav-link">
                    <span class="menu-icon"><i data-lucide="square-pi"></i></span>
                    <span class="menu-text" data-lang="forms">Forms</span>
                    <span class="menu-arrow"></span>
                </a>
                <div class="collapse" id="sidebarForms">
                    <ul class="sub-menu">
                        <li class="side-nav-item">
                            <a href=@Url.Action("Elements", "Form") class="side-nav-link">
                                <span class="menu-text" data-lang="form-elements">Basic Elements</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("Plugins", "Form") class="side-nav-link">
                                <span class="menu-text" data-lang="form-plugins">Plugins</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("Validation", "Form") class="side-nav-link">
                                <span class="menu-text" data-lang="form-validation">Validation</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("Wizard", "Form") class="side-nav-link">
                                <span class="menu-text" data-lang="form-wizard">Wizard</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("Fileuploads", "Form") class="side-nav-link">
                                <span class="menu-text" data-lang="form-fileuploads">File Uploads</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("QuillEditor", "Form") class="side-nav-link">
                                <span class="menu-text" data-lang="form-quilljs">Quilljs Editors</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </li>

            <li class="side-nav-item">
                <a data-bs-toggle="collapse" href="#sidebarTables" aria-expanded="false" aria-controls="sidebarTables"
                   class="side-nav-link">
                    <span class="menu-icon"><i data-lucide="table-2"></i></span>
                    <span class="menu-text" data-lang="tables">Tables</span>
                    <span class="menu-arrow"></span>
                </a>
                <div class="collapse" id="sidebarTables">
                    <ul class="sub-menu">
                        <li class="side-nav-item">
                            <a href=@Url.Action("Static", "Tables") class="side-nav-link">
                                <span class="menu-text" data-lang="tables-static">Static Tables</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a data-bs-toggle="collapse" href="#sidebarDataTables" aria-expanded="false"
                               aria-controls="sidebarDataTables" class="side-nav-link">
                                <span class="menu-text" data-lang="datatables">DataTables</span>
                                <span class="badge bg-success">09</span>
                            </a>
                            <div class="collapse" id="sidebarDataTables">
                                <ul class="sub-menu">
                                    <li class="side-nav-item">
                                        <a href=@Url.Action("DatatablesBasic", "Tables") class="side-nav-link">
                                            <span class="menu-text" data-lang="tables-datatables-basic">Basic</span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item">
                                        <a href=@Url.Action("DatatablesExportData", "Tables") class="side-nav-link">
                                            <span class="menu-text" data-lang="tables-datatables-export-data">Export Data</span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item">
                                        <a href=@Url.Action("DatatablesSelect", "Tables") class="side-nav-link">
                                            <span class="menu-text" data-lang="tables-datatables-select">Select</span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item">
                                        <a href=@Url.Action("DatatablesAjax", "Tables") class="side-nav-link">
                                            <span class="menu-text" data-lang="tables-datatables-ajax">Ajax</span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item">
                                        <a href=@Url.Action("DatatablesJavascript", "Tables") class="side-nav-link">
                                            <span class="menu-text" data-lang="tables-datatables-javascript">Javascript Source</span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item">
                                        <a href=@Url.Action("DatatablesRendering", "Tables") class="side-nav-link">
                                            <span class="menu-text" data-lang="tables-datatables-rendering">Data Rendering</span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item">
                                        <a href=@Url.Action("DatatablesColumns", "Tables") class="side-nav-link">
                                            <span class="menu-text" data-lang="tables-datatables-columns">Show & Hide Column</span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item">
                                        <a href=@Url.Action("DatatablesChildRows", "Tables") class="side-nav-link">
                                            <span class="menu-text"
                                                  data-lang="tables-datatables-child-rows">Child Rows</span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item">
                                        <a href=@Url.Action("DatatablesCheckboxSelect", "Tables") class="side-nav-link">
                                            <span class="menu-text" data-lang="tables-datatables-checkbox-select">Checkbox Select</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                    </ul>
                </div>
            </li>

            <li class="side-nav-item">
                <a data-bs-toggle="collapse" href="#sidebarIcons" aria-expanded="false" aria-controls="sidebarIcons"
                   class="side-nav-link">
                    <span class="menu-icon"><i data-lucide="layers-2"></i></span>
                    <span class="menu-text" data-lang="icons">Icons</span>
                    <span class="menu-arrow"></span>
                </a>
                <div class="collapse" id="sidebarIcons">
                    <ul class="sub-menu">
                        <li class="side-nav-item">
                            <a href=@Url.Action("Tabler", "Icons") class="side-nav-link">
                                <span class="menu-text" data-lang="icons-tabler">Tabler</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("Lucide", "Icons") class="side-nav-link">
                                <span class="menu-text" data-lang="icons-lucide">Lucide</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("Flags", "Icons") class="side-nav-link">
                                <span class="menu-text" data-lang="icons-flags">Flags</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </li>

            <li class="side-nav-item">
                <a data-bs-toggle="collapse" href="#sidebarMaps" aria-expanded="false" aria-controls="sidebarMaps"
                   class="side-nav-link">
                    <span class="menu-icon"><i data-lucide="map-pin"></i></span>
                    <span class="menu-text" data-lang="maps">Maps</span>
                    <span class="menu-arrow"></span>
                </a>
                <div class="collapse" id="sidebarMaps">
                    <ul class="sub-menu">
                        <li class="side-nav-item">
                            <a href=@Url.Action("Vector", "Maps") class="side-nav-link">
                                <span class="menu-text" data-lang="maps-vector">Vector Maps</span>
                            </a>
                        </li>
                        <li class="side-nav-item">
                            <a href=@Url.Action("Leaflet", "Maps") class="side-nav-link">
                                <span class="menu-text" data-lang="maps-leaflet">Leaflet Maps</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </li>

            <li class="side-nav-title mt-2" data-lang="items-title">Menu Items</li>

            <li class="side-nav-item">
                <a data-bs-toggle="collapse" href="#sidebarMenuLevels" aria-expanded="false"
                   aria-controls="sidebarMenuLevels" class="side-nav-link">
                    <span class="menu-icon"><i data-lucide="command"></i></span>
                    <span class="menu-text" data-lang="menu-levels"> Menu Levels </span>
                    <span class="menu-arrow"></span>
                </a>
                <div class="collapse" id="sidebarMenuLevels">
                    <ul class="sub-menu">
                        <li class="side-nav-item">
                            <a data-bs-toggle="collapse" href="#sidebarSecondLevel" aria-expanded="false"
                               aria-controls="sidebarSecondLevel" class="side-nav-link">
                                <span class="menu-text" data-lang="second-level"> Second Level </span>
                                <span class="menu-arrow"></span>
                            </a>
                            <div class="collapse" id="sidebarSecondLevel">
                                <ul class="sub-menu">
                                    <li class="side-nav-item">
                                        <a href="javascript: void(0);" class="side-nav-link">
                                            <span class="menu-text">Item 2.1</span>
                                        </a>
                                    </li>
                                    <li class="side-nav-item">
                                        <a href="javascript: void(0);" class="side-nav-link">
                                            <span class="menu-text">Item 2.2</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li class="side-nav-item">
                            <a data-bs-toggle="collapse" href="#sidebarThirdLevel" aria-expanded="false"
                               aria-controls="sidebarThirdLevel" class="side-nav-link">
                                <span class="menu-text" data-lang="third-level"> Third Level </span>
                                <span class="menu-arrow"></span>
                            </a>
                            <div class="collapse" id="sidebarThirdLevel">
                                <ul class="sub-menu">
                                    <li class="side-nav-item">
                                        <a href="javascript: void(0);" class="side-nav-link">Item 1</a>
                                    </li>
                                    <li class="side-nav-item">
                                        <a data-bs-toggle="collapse" href="#sidebarFourthLevel" aria-expanded="false"
                                           aria-controls="sidebarFourthLevel" class="side-nav-link">
                                            <span class="menu-text"> Item 2 </span>
                                            <span class="menu-arrow"></span>
                                        </a>
                                        <div class="collapse" id="sidebarFourthLevel">
                                            <ul class="sub-menu">
                                                <li class="side-nav-item">
                                                    <a href="javascript: void(0);" class="side-nav-link">
                                                        <span class="menu-text">Item 3.1</span>
                                                    </a>
                                                </li>
                                                <li class="side-nav-item">
                                                    <a href="javascript: void(0);" class="side-nav-link">
                                                        <span class="menu-text">Item 3.2</span>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </li>
                    </ul>
                </div>
            </li>

            <li class="side-nav-item">
                <a href="#!" class="side-nav-link disabled">
                    <span class="menu-icon"><i data-lucide="shield-ban"></i></span>
                    <span class="menu-text" data-lang="disabled-menu"> Disabled Menu </span>
                </a>
            </li>
        </ul>
    </div>

    <div class="menu-collapse-box d-none d-xl-block">
        <button class="button-collapse-toggle">
            <i data-lucide="square-chevron-left" class="align-middle flex-shrink-0"></i> <span>Collapse Menu</span>
        </button>
    </div>
</div>
<!-- Sidenav Menu End -->

<script>
    // Note: If you do not want any of this logic here, you can remove it. It's already in app.js. This is for removing delays.

    // Sidenav Icons
    lucide.createIcons();


    // Sidenav Link Activation
    const currentUrlT = window.location.href.split(/[?#]/)[0];
    const currentPageT = window.location.pathname.split("/").pop();
    const sideNavT = document.querySelector('.side-nav');

    document.querySelectorAll('.side-nav-link[href]').forEach(link => {
        const linkHref = link.getAttribute('href');
        if (!linkHref) return;

        const match = linkHref === currentPageT || link.href === currentUrlT;

        if (match) {
            // Mark link and its li active
            link.classList.add('active');
            const li = link.closest('li.side-nav-item');
            if (li) li.classList.add('active');

            // Expand all parent .collapse and set toggles
            let parentCollapse = link.closest('.collapse');
            while (parentCollapse) {
                parentCollapse.classList.add('show');

                const parentToggle = document.querySelector(`a[href="#${parentCollapse.id}"]`);
                if (parentToggle) {
                    parentToggle.setAttribute('aria-expanded', 'true');
                    const parentLi = parentToggle.closest('li.side-nav-item');
                    if (parentLi) parentLi.classList.add('active');
                }

                parentCollapse = parentCollapse.parentElement.closest('.collapse');
            }
        }
    });
</script>